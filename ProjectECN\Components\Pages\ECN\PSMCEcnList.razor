@page "/ecn/psmc"
@rendermode InteractiveServer
@using ProjectECN.DTO
@using ProjectECN.Services
@inject PSMCEcnDataService DataService
@inject IJSRuntime JSRuntime

<div class="psmc-ecn-container">
    <div class="page-header">
        <h2>🏭 PSMC ECN Processing Results</h2>
        <p class="page-description">ECNs with OTHERS checkbox marked and PSMC text detected</p>
    </div>

    <div class="summary-cards">
        @if (summary != null)
        {
            <div class="summary-card">
                <div class="card-icon">📊</div>
                <div class="card-content">
                    <h3>@summary.PSMCQualified</h3>
                    <p>PSMC Qualified ECNs</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">📋</div>
                <div class="card-content">
                    <h3>@summary.TotalProcessed</h3>
                    <p>Total Processed</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">✅</div>
                <div class="card-content">
                    <h3>@summary.OthersCheckboxMarked</h3>
                    <p>Others Checkbox Marked</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">🔍</div>
                <div class="card-content">
                    <h3>@summary.PSMCTextDetected</h3>
                    <p>PSMC Text Detected</p>
                </div>
            </div>
        }
    </div>

    <div class="controls-section">
        <div class="search-controls">
            <input type="text" @bind="searchTerm" @onkeyup="HandleSearch" placeholder="Search ECN number..." class="search-input" />
            <button type="button" @onclick="HandleSearch" class="search-button">🔍 Search</button>
        </div>
        
        <div class="filter-controls">
            <select @bind="selectedVehicleType" @bind:after="HandleFilterChange" class="filter-select">
                <option value="">All Vehicle Types</option>
                <option value="2W">2W (Two Wheeler)</option>
                <option value="4W">4W (Four Wheeler)</option>
            </select>

            <input type="date" @bind="startDate" @bind:after="HandleFilterChange" class="date-input" />
            <span>to</span>
            <input type="date" @bind="endDate" @bind:after="HandleFilterChange" class="date-input" />
        </div>
        
        <div class="action-controls">
            <button type="button" @onclick="RefreshData" class="refresh-button">🔄 Refresh</button>
            <button type="button" @onclick="ExportToCsv" class="export-button">📥 Export CSV</button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading PSMC ECN data...</p>
        </div>
    }
    else if (filteredEcns.Any())
    {
        <div class="ecn-grid">
            @foreach (var ecn in filteredEcns)
            {
                <div class="ecn-card @(ecn.QualifiesForPSMC ? "qualified" : "")">
                    <div class="ecn-header">
                        <h3>@ecn.EcnNumber</h3>
                        <span class="vehicle-badge @ecn.VehicleType.ToLower()">@ecn.VehicleType</span>
                    </div>
                    
                    <div class="ecn-details">
                        <div class="detail-row">
                            <span class="label">File:</span>
                            <span class="value">@ecn.FileName</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="label">Process Date:</span>
                            <span class="value">@ecn.ProcessDateTime.ToString("yyyy-MM-dd")</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="label">OCR Confidence:</span>
                            <span class="value">@ecn.OcrConfidence.ToString("F1")%</span>
                        </div>
                    </div>
                    
                    <div class="status-indicators">
                        <div class="status-item @(ecn.IsOthersCheckboxMarked ? "active" : "")">
                            <span class="status-icon">@(ecn.IsOthersCheckboxMarked ? "✅" : "❌")</span>
                            <span>Others Checkbox</span>
                        </div>
                        
                        <div class="status-item @(ecn.HasPSMCText ? "active" : "")">
                            <span class="status-icon">@(ecn.HasPSMCText ? "✅" : "❌")</span>
                            <span>PSMC Text</span>
                        </div>
                    </div>
                    
                    @if (ecn.ProcessingNotes.Any())
                    {
                        <div class="processing-notes">
                            <strong>Notes:</strong>
                            <ul>
                                @foreach (var note in ecn.ProcessingNotes)
                                {
                                    <li>@note</li>
                                }
                            </ul>
                        </div>
                    }
                    
                    <div class="ecn-actions">
                        <button type="button" @onclick="() => ViewImages(ecn)" class="view-images-button">
                            🖼️ View Images
                        </button>
                        <button type="button" @onclick="() => ViewOcrText(ecn)" class="view-ocr-button">
                            📄 View OCR Text
                        </button>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="no-data">
            <div class="no-data-icon">📭</div>
            <h3>No PSMC ECNs Found</h3>
            <p>No ECNs match the current filters or no data has been processed yet.</p>
        </div>
    }
</div>

@if (showOcrModal && selectedEcn != null)
{
    <div class="modal-overlay" @onclick="CloseOcrModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>OCR Text - @selectedEcn.EcnNumber</h3>
                <button type="button" @onclick="CloseOcrModal" class="close-button">✕</button>
            </div>
            <div class="modal-body">
                <div class="ocr-text">
                    <pre>@selectedEcn.FactoryOcrText</pre>
                </div>
                <div class="ocr-details">
                    <p><strong>Confidence:</strong> @selectedEcn.OcrConfidence.ToString("F2")%</p>
                    <p><strong>Detected Factories:</strong> @string.Join(", ", selectedEcn.DetectedFactories)</p>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<PSMCEcnDto> allEcns = new();
    private List<PSMCEcnDto> filteredEcns = new();
    private PSMCProcessingSummary? summary;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string selectedVehicleType = string.Empty;
    private DateTime? startDate;
    private DateTime? endDate;
    private bool showOcrModal = false;
    private PSMCEcnDto? selectedEcn;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            allEcns = await DataService.GetQualifiedPSMCEcnsAsync();
            summary = await DataService.GetProcessingSummaryAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading PSMC ECN data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void HandleSearch()
    {
        ApplyFilters();
    }

    private void HandleFilterChange()
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        var filtered = allEcns.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filtered = filtered.Where(e =>
                e.EcnNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                e.FileName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply vehicle type filter
        if (!string.IsNullOrWhiteSpace(selectedVehicleType))
        {
            filtered = filtered.Where(e =>
                e.VehicleType.Equals(selectedVehicleType, StringComparison.OrdinalIgnoreCase));
        }

        // Apply date range filter
        if (startDate.HasValue)
        {
            filtered = filtered.Where(e => e.ProcessDateTime >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            filtered = filtered.Where(e => e.ProcessDateTime <= endDate.Value);
        }

        filteredEcns = filtered.OrderByDescending(e => e.ProcessedAt).ToList();
        StateHasChanged();
    }

    private async Task ExportToCsv()
    {
        try
        {
            var csv = await DataService.ExportPSMCEcnsToCsvAsync();
            var fileName = $"psmc-ecns-{DateTime.Now:yyyyMMdd-HHmmss}.csv";
            
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, "text/csv", csv);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error exporting CSV: {ex.Message}");
        }
    }

    private void ViewImages(PSMCEcnDto ecn)
    {
        // TODO: Implement image viewer modal
        Console.WriteLine($"View images for ECN: {ecn.EcnNumber}");
    }

    private void ViewOcrText(PSMCEcnDto ecn)
    {
        selectedEcn = ecn;
        showOcrModal = true;
        StateHasChanged();
    }

    private void CloseOcrModal()
    {
        showOcrModal = false;
        selectedEcn = null;
        StateHasChanged();
    }
}
