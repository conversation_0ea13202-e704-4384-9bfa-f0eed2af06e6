using Microsoft.EntityFrameworkCore;
using ProjectECN.Models;

namespace ProjectECN.Services;

public class ECNDatabaseService(
    IDbContextFactory<ApplicationDbContext> contextFactory,
    ILogger<ECNDatabaseService> logger)
{
    /// <summary>
    /// Save ECN data to database
    /// </summary>
    public async Task<DatabaseOperationResult> SaveECNDataAsync(List<RawEcndatum> ecnData)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();

            //foreach (var ee in ecnData)
            //{
            //    ee.CreatedDate = new DateTime(2025, 04, 22);
            //}

            logger.LogInformation($"Saving {ecnData.Count} ECN records to database");

            // Add all records
            await context.RawEcndata.AddRangeAsync(ecnData);
            var savedCount = await context.SaveChangesAsync();

            logger.LogInformation($"Successfully saved {savedCount} ECN records to database");

            return new DatabaseOperationResult
            {
                Success = true,
                Message = $"Successfully saved {savedCount} ECN records",
                RecordsAffected = savedCount
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving ECN data to database");
            return new DatabaseOperationResult
            {
                Success = false,
                ErrorMessage = $"Error saving ECN data: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// Delete ECN data by created date (for re-upload scenarios)
    /// </summary>
    public async Task<DatabaseOperationResult> DeleteECNDataByDateAsync(DateTime createdDate)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();
            
            // Delete records with the same created date
            var recordsToDelete = await context.RawEcndata
                .Where(r => r.CreatedDate != null && r.CreatedDate.Value.Date == createdDate.Date)
                .ToListAsync();

            if (recordsToDelete.Any())
            {
                logger.LogInformation($"Deleting {recordsToDelete.Count} existing ECN records for date {createdDate:yyyy-MM-dd}");
                
                context.RawEcndata.RemoveRange(recordsToDelete);
                var deletedCount = await context.SaveChangesAsync();

                logger.LogInformation($"Successfully deleted {deletedCount} ECN records");

                return new DatabaseOperationResult
                {
                    Success = true,
                    Message = $"Successfully deleted {deletedCount} existing ECN records",
                    RecordsAffected = deletedCount
                };
            }
            else
            {
                logger.LogInformation($"No existing ECN records found for date {createdDate:yyyy-MM-dd}");
                return new DatabaseOperationResult
                {
                    Success = true,
                    Message = "No existing records found to delete",
                    RecordsAffected = 0
                };
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error deleting ECN data for date {createdDate:yyyy-MM-dd}");
            return new DatabaseOperationResult
            {
                Success = false,
                ErrorMessage = $"Error deleting ECN data: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// Check if ECN data exists for a specific date
    /// </summary>
    public async Task<bool> ECNDataExistsForDateAsync(DateTime createdDate)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();
            
            var exists = await context.RawEcndata
                .AnyAsync(r => r.CreatedDate != null && r.CreatedDate.Value.Date == createdDate.Date);

            logger.LogInformation($"ECN data exists for date {createdDate:yyyy-MM-dd}: {exists}");
            return exists;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error checking ECN data existence for date {createdDate:yyyy-MM-dd}");
            return false;
        }
    }

    /// <summary>
    /// Get ECN data count by date
    /// </summary>
    public async Task<int> GetECNDataCountByDateAsync(DateTime createdDate)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();
            
            var count = await context.RawEcndata
                .CountAsync(r => r.CreatedDate != null && r.CreatedDate.Value.Date == createdDate.Date);

            logger.LogInformation($"ECN data count for date {createdDate:yyyy-MM-dd}: {count}");
            return count;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error getting ECN data count for date {createdDate:yyyy-MM-dd}");
            return 0;
        }
    }

    /// <summary>
    /// Get ECN data for a specific date range
    /// </summary>
    public async Task<List<RawEcndatum>> GetECNDataByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();
            
            var data = await context.RawEcndata
                .Where(r => r.CreatedDate != null && r.CreatedDate.Value.Date >= startDate.Date && r.CreatedDate.Value.Date <= endDate.Date)
                .OrderBy(r => r.CreatedDate)
                .ThenBy(r => r.EcnNo)
                .ToListAsync();

            logger.LogInformation($"Retrieved {data.Count} ECN records for date range {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            return data;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error retrieving ECN data for date range {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            return new List<RawEcndatum>();
        }
    }

    /// <summary>
    /// Get PSMC ECN data for a specific date
    /// </summary>
    public async Task<List<RawEcndatum>> GetPSMCECNDataByDateAsync(DateTime createdDate)
    {
        try
        {
            await using var context = await contextFactory.CreateDbContextAsync();
            
            var data = await context.RawEcndata
                .Where(r => r.CreatedDate != null && r.CreatedDate.Value.Date == createdDate.Date && r.BelongsToPsmc==true)
                .OrderBy(r => r.EcnNo)
                .ToListAsync();

            logger.LogInformation($"Retrieved {data.Count} PSMC ECN records for date {createdDate:yyyy-MM-dd}");
            return data;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error retrieving PSMC ECN data for date {createdDate:yyyy-MM-dd}");
            return new List<RawEcndatum>();
        }
    }
}

public class DatabaseOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int RecordsAffected { get; set; }
}
