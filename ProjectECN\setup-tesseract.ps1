# PowerShell script to download Tesseract language data
$tessDataDir = "tessdata"
$engDataUrl = "https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata"
$engDataFile = "$tessDataDir\eng.traineddata"

# Create tessdata directory if it doesn't exist
if (!(Test-Path $tessDataDir)) {
    New-Item -ItemType Directory -Path $tessDataDir -Force
    Write-Host "Created tessdata directory: $tessDataDir"
}

# Download English language data if it doesn't exist
if (!(Test-Path $engDataFile)) {
    Write-Host "Downloading Tesseract English language data..."
    try {
        Invoke-WebRequest -Uri $engDataUrl -OutFile $engDataFile
        Write-Host "Successfully downloaded: $engDataFile"
        Write-Host "File size: $((Get-Item $engDataFile).Length) bytes"
    }
    catch {
        Write-Error "Failed to download Tesseract language data: $_"
        exit 1
    }
} else {
    Write-Host "Tesseract English language data already exists: $engDataFile"
}

Write-Host "Tesseract setup completed successfully!"
