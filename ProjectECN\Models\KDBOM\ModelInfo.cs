using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectECN.Models.KDBOM;

[Table("ModelInfo", Schema = "kdbom")]
public class ModelInfo
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid FileUploadId { get; set; }

    [Required]
    [MaxLength(100)]
    public string SheetName { get; set; } = string.Empty;

    [MaxLength(50)]
    public string? ModelCode { get; set; }

    [MaxLength(255)]
    public string? ModelName { get; set; }

    [MaxLength(500)]
    public string? ModelDescription { get; set; }

    [MaxLength(10)]
    public string? ModelYear { get; set; }

    [MaxLength(50)]
    public string? ModelType { get; set; }

    public string? AdditionalInfo { get; set; } // JSON for any additional model data

    public DateTime CreatedDate { get; set; } = DateTime.Now;

    [Required]
    [MaxLength(100)]
    public string CreatedBy { get; set; } = string.Empty;

    // Navigation property
    [ForeignKey("FileUploadId")]
    public virtual FileUpload FileUpload { get; set; } = null!;
}
