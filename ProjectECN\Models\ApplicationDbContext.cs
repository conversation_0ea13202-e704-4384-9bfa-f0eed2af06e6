﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace ProjectECN.Models;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<RawEcndatum> RawEcndata { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<RawEcndatum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__RawECNDa__3213E83F8A3D557E");

            entity.ToTable("RawECNData", "raw");

            entity.Property(e => e.Id)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("id");
            entity.Property(e => e.BelongsToPsmc)
                .HasDefaultValue(false)
                .HasColumnName("belongs_to_psmc");
            entity.Property(e => e.Block)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("block");
            entity.Property(e => e.BomGname)
                .HasMaxLength(40)
                .IsUnicode(false)
                .HasColumnName("bom_gname");
            entity.Property(e => e.CcCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cc_code");
            entity.Property(e => e.CreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("created_date");
            entity.Property(e => e.DwgEcnNo)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("dwg_ecn_no");
            entity.Property(e => e.DwgNumber)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("dwg_number");
            entity.Property(e => e.EcnNo)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("ecn_no");
            entity.Property(e => e.FileName)
                .HasMaxLength(600)
                .IsUnicode(false)
                .HasColumnName("file_name");
            entity.Property(e => e.Line)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("line");
            entity.Property(e => e.NewPartNumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("new_part_number");
            entity.Property(e => e.OldPartNumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("old_part_number");
            entity.Property(e => e.Page)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("page");
            entity.Property(e => e.PartName)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("part_name");
            entity.Property(e => e.ProdType)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("prod_type");
            entity.Property(e => e.RecordCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("record_created_by");
            entity.Property(e => e.RecordCreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("record_created_date");
            entity.Property(e => e.RecordModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("record_modified_by");
            entity.Property(e => e.RecordModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("record_modified_date");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}