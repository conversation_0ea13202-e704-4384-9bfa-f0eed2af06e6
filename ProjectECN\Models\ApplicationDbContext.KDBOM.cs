using Microsoft.EntityFrameworkCore;
using ProjectECN.Models.KDBOM;

namespace ProjectECN.Models;

// Partial class to extend ApplicationDbContext with KDBOM entities
public partial class ApplicationDbContext
{
    // KDBOM DbSets
    public virtual DbSet<FileUpload> KDBOMFileUploads { get; set; }
    public virtual DbSet<ModelInfo> KDBOMModelInfos { get; set; }
    public virtual DbSet<PartsData> KDBOMPartsData { get; set; }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder)
    {
        // Configure KDBOM entities
        ConfigureKDBOMEntities(modelBuilder);
    }

    private void ConfigureKDBOMEntities(ModelBuilder modelBuilder)
    {
        // FileUpload configuration
        modelBuilder.Entity<FileUpload>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("NEWID()");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.UploadedDate).HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ProcessingStatus).HasDefaultValue("Pending");
        });

        // ModelInfo configuration
        modelBuilder.Entity<ModelInfo>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("NEWID()");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");
            
            entity.HasOne(e => e.FileUpload)
                  .WithMany(f => f.ModelInfos)
                  .HasForeignKey(e => e.FileUploadId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // PartsData configuration
        modelBuilder.Entity<PartsData>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("NEWID()");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");
            
            entity.HasOne(e => e.FileUpload)
                  .WithMany(f => f.PartsData)
                  .HasForeignKey(e => e.FileUploadId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Create indexes for better performance
        modelBuilder.Entity<FileUpload>()
            .HasIndex(e => e.UploadedDate)
            .HasDatabaseName("IX_FileUploads_UploadedDate");

        modelBuilder.Entity<FileUpload>()
            .HasIndex(e => e.ProcessingStatus)
            .HasDatabaseName("IX_FileUploads_Status");

        modelBuilder.Entity<ModelInfo>()
            .HasIndex(e => e.FileUploadId)
            .HasDatabaseName("IX_ModelInfo_FileUploadId");

        modelBuilder.Entity<PartsData>()
            .HasIndex(e => e.FileUploadId)
            .HasDatabaseName("IX_PartsData_FileUploadId");

        modelBuilder.Entity<PartsData>()
            .HasIndex(e => e.KDModelCode)
            .HasDatabaseName("IX_PartsData_KDModelCode");

        modelBuilder.Entity<PartsData>()
            .HasIndex(e => e.PartNo)
            .HasDatabaseName("IX_PartsData_PartNo");

        modelBuilder.Entity<PartsData>()
            .HasIndex(e => e.SheetName)
            .HasDatabaseName("IX_PartsData_SheetName");
    }
}
