using OfficeOpenXml;

namespace ProjectECN;

public class TestExcelReading
{
    public static async Task TestExcelFile()
    {
        try
        {
            // Set license context
            if (ExcelPackage.LicenseContext == LicenseContext.Commercial)
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            var testFilePath =
                @"E:\OfficeProjects\ProjectECN\ProjectECN\wwwroot\enc\20250422\2W\Other\<EMAIL>";

            if (!File.Exists(testFilePath))
            {
                Console.WriteLine($"Test file not found: {testFilePath}");
                return;
            }

            Console.WriteLine($"Testing Excel file: {testFilePath}");

            using var package = new ExcelPackage(new FileInfo(testFilePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                Console.WriteLine("No worksheet found");
                return;
            }

            Console.WriteLine($"Worksheet name: {worksheet.Name}");
            Console.WriteLine($"Dimensions: {worksheet.Dimension?.Address}");

            // Read header information
            var ccCode = worksheet.Cells[1, 2].Value?.ToString();
            var prodType = worksheet.Cells[2, 2].Value?.ToString();
            var bomGname = worksheet.Cells[3, 2].Value?.ToString();
            var createDate = worksheet.Cells[4, 2].Value?.ToString();

            Console.WriteLine($"CC Code: {ccCode}");
            Console.WriteLine($"Prod Type: {prodType}");
            Console.WriteLine($"BOM GName: {bomGname}");
            Console.WriteLine($"Create Date: {createDate}");

            // Read first few data rows
            Console.WriteLine("\nFirst 5 data rows:");
            for (var row = 5; row <= Math.Min(9, worksheet.Dimension?.End.Row ?? 5); row++)
            {
                var ecnNo = worksheet.Cells[row, 1].Value?.ToString();
                var page = worksheet.Cells[row, 2].Value?.ToString();
                var line = worksheet.Cells[row, 3].Value?.ToString();
                var block = worksheet.Cells[row, 4].Value?.ToString();
                var oldPart = worksheet.Cells[row, 5].Value?.ToString();
                var newPart = worksheet.Cells[row, 6].Value?.ToString();
                var partName = worksheet.Cells[row, 7].Value?.ToString();

                Console.WriteLine(
                    $"Row {row}: ECN={ecnNo}, Page={page}, Line={line}, Block={block}, OldPart={oldPart}, NewPart={newPart}, PartName={partName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error testing Excel file: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}