using Microsoft.AspNetCore.Mvc;
using ProjectECN.Services;
using ProjectECN.DTO;

namespace ProjectECN.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ECNExcelProcessingService _excelService;
    private readonly ECNDatabaseService _databaseService;
    private readonly ILogger<TestController> _logger;

    public TestController(ECNExcelProcessingService excelService, ECNDatabaseService databaseService, ILogger<TestController> logger)
    {
        _excelService = excelService;
        _databaseService = databaseService;
        _logger = logger;
    }

    [HttpGet("excel")]
    public async Task<IActionResult> TestExcel()
    {
        try
        {
            await TestExcelReading.TestExcelFile();
            return Ok("Excel test completed - check console output");
        }
        catch (Exception ex)
        {
            return BadRequest($"Excel test failed: {ex.Message}");
        }
    }

    [HttpGet("excel-service")]
    public async Task<IActionResult> TestExcelService()
    {
        try
        {
            var extractFolderPath = @"E:\OfficeProjects\ProjectECN\ProjectECN\wwwroot\enc\20250422";
            var psmcEcns = new List<PSMCEcnDto>(); // Empty list for testing
            var currentUserId = "test-user";

            var result = await _excelService.ProcessECNExcelFilesAsync(extractFolderPath, psmcEcns, currentUserId);

            return Ok(new
            {
                Success = result.Success,
                Message = result.Message,
                ErrorMessage = result.ErrorMessage,
                ProcessedFiles = result.ProcessedFiles,
                TotalFiles = result.TotalFiles,
                RecordCount = result.EcnData.Count,
                SampleRecords = result.EcnData.Take(5).Select(r => new
                {
                    r.FileName,
                    r.EcnNo,
                    r.CcCode,
                    r.ProdType,
                    r.BomGname,
                    r.CreatedDate,
                    r.Page,
                    r.Line,
                    r.Block,
                    r.OldPartNumber,
                    r.NewPartNumber,
                    r.PartName
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing Excel service");
            return BadRequest($"Excel service test failed: {ex.Message}");
        }
    }

    [HttpGet("database")]
    public async Task<IActionResult> TestDatabase()
    {
        try
        {
            var testDate = new DateTime(2025, 4, 22);
            var count = await _databaseService.GetECNDataCountByDateAsync(testDate);
            var exists = await _databaseService.ECNDataExistsForDateAsync(testDate);

            return Ok(new
            {
                Date = testDate.ToString("yyyy-MM-dd"),
                RecordCount = count,
                DataExists = exists
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing database service");
            return BadRequest($"Database test failed: {ex.Message}");
        }
    }
}
