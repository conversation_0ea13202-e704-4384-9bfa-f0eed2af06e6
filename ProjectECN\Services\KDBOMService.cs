using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using ProjectECN.Models;
using ProjectECN.Models.KDBOM;
using ProjectECN.DTO.KDBOM;
using System.Diagnostics;

namespace ProjectECN.Services;

public class KDBOMService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
    private readonly ILogger<KDBOMService> _logger;
    private readonly IWebHostEnvironment _environment;

    public KDBOMService(
        IDbContextFactory<ApplicationDbContext> contextFactory,
        ILogger<KDBOMService> logger,
        IWebHostEnvironment environment)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// Process multiple KDBOM Excel files and import to database
    /// </summary>
    public async Task<KDBOMUploadResult> ProcessKDBOMFilesAsync(
        List<Microsoft.AspNetCore.Components.Forms.IBrowserFile> files,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KDBOMUploadResult();

        try
        {
            _logger.LogInformation($"Starting KDBOM processing for {files.Count} files by user: {uploadedBy}");

            // Clear existing data first
            await ClearExistingKDBOMDataAsync();
            progress?.Report(new KDBOMUploadProgress { Stage = "Cleared existing data", Percentage = 5 });

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;
            var processedSheets = new List<string>();

            for (int i = 0; i < files.Count; i++)
            {
                var file = files[i];
                var fileProgress = (i * 90) / files.Count + 10; // 10-100% range

                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing file {i + 1} of {files.Count}",
                    Percentage = fileProgress,
                    CurrentFile = file.Name
                });

                try
                {
                    var fileResult = await ProcessSingleKDBOMFileAsync(file, uploadedBy, progress);
                    if (fileResult.Success)
                    {
                        result.ProcessedFiles++;
                        totalRecords += fileResult.TotalRecords;
                        modelInfoRecords += fileResult.ModelInfoRecords;
                        partsDataRecords += fileResult.PartsDataRecords;
                        processedSheets.AddRange(fileResult.ProcessedSheets);
                    }
                    else
                    {
                        result.Errors.Add($"File {file.Name}: {fileResult.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing file {file.Name}");
                    result.Errors.Add($"File {file.Name}: {ex.Message}");
                }
            }

            result.Success = result.ProcessedFiles > 0;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;
            result.ProcessedSheets = processedSheets;
            result.ProcessingTime = stopwatch.Elapsed;

            if (result.Success)
            {
                result.Message = $"Successfully processed {result.ProcessedFiles} files with {result.TotalRecords} total records";
                _logger.LogInformation(result.Message);
            }
            else
            {
                result.ErrorMessage = "No files were processed successfully";
                _logger.LogWarning(result.ErrorMessage);
            }

            progress?.Report(new KDBOMUploadProgress { Stage = "Processing completed", Percentage = 100 });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in KDBOM processing");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// Process a single KDBOM Excel file
    /// </summary>
    private async Task<KDBOMUploadResult> ProcessSingleKDBOMFileAsync(
        Microsoft.AspNetCore.Components.Forms.IBrowserFile file,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var result = new KDBOMUploadResult();

        await using var context = await _contextFactory.CreateDbContextAsync();
        using var transaction = await context.Database.BeginTransactionAsync();

        try
        {
            // Create file upload record
            var fileUpload = new FileUpload
            {
                FileName = Path.GetFileNameWithoutExtension(file.Name),
                OriginalFileName = file.Name,
                FilePath = $"kdbom/{file.Name}",
                FileSize = file.Size,
                UploadedBy = uploadedBy,
                CreatedBy = uploadedBy,
                ProcessingStatus = "Processing"
            };

            context.KDBOMFileUploads.Add(fileUpload);
            await context.SaveChangesAsync();

            // Process Excel file - increase size limit to 200MB and use memory stream to avoid synchronous read issues
            const long maxFileSize = 200 * 1024 * 1024; // 200MB limit
            using var memoryStream = new MemoryStream();
            await file.OpenReadStream(maxFileSize).CopyToAsync(memoryStream);
            memoryStream.Position = 0;
            using var package = new ExcelPackage(memoryStream);

            var worksheets = package.Workbook.Worksheets;
            if (worksheets.Count == 0)
            {
                throw new InvalidOperationException("No worksheets found in the Excel file");
            }

            _logger.LogInformation($"Found {worksheets.Count} worksheets in file {file.Name}");

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;

            // Process first sheet as model info
            var firstSheet = worksheets[0];
            progress?.Report(new KDBOMUploadProgress
            {
                Stage = "Processing model information",
                CurrentSheet = firstSheet.Name
            });

            var modelInfo = ProcessModelInfoSheet(firstSheet, fileUpload.Id, uploadedBy);
            if (modelInfo != null)
            {
                context.KDBOMModelInfos.Add(modelInfo);
                modelInfoRecords++;
                result.ProcessedSheets.Add($"{firstSheet.Name} (Model Info)");
            }

            // Process remaining sheets as parts data
            for (int i = 1; i < worksheets.Count; i++)
            {
                var worksheet = worksheets[i];
                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing parts data sheet {i}",
                    CurrentSheet = worksheet.Name
                });

                var partsData = ProcessPartsDataSheet(worksheet, fileUpload.Id, uploadedBy);
                if (partsData.Any())
                {
                    context.KDBOMPartsData.AddRange(partsData);
                    partsDataRecords += partsData.Count;
                    result.ProcessedSheets.Add($"{worksheet.Name} ({partsData.Count} parts)");
                    _logger.LogInformation($"Processed sheet {worksheet.Name} with {partsData.Count} parts");
                }
                else
                {
                    _logger.LogWarning($"No parts data found in sheet {worksheet.Name}");
                }
            }

            totalRecords = modelInfoRecords + partsDataRecords;

            // Update file upload record
            fileUpload.ProcessedDate = DateTime.Now;
            fileUpload.ProcessingStatus = "Completed";
            fileUpload.RecordCount = totalRecords;

            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            result.Success = true;
            result.ProcessedFiles = 1;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;

            _logger.LogInformation($"Successfully processed file {file.Name} with {totalRecords} records");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, $"Error processing file {file.Name}");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// Process model information from the first sheet
    /// </summary>
    private ModelInfo? ProcessModelInfoSheet(ExcelWorksheet worksheet, Guid fileUploadId, string createdBy)
    {
        try
        {
            // Extract model information from the first sheet
            // This is a basic implementation - you may need to adjust based on actual sheet structure
            var modelInfo = new ModelInfo
            {
                FileUploadId = fileUploadId,
                SheetName = worksheet.Name,
                CreatedBy = createdBy
            };

            // Try to extract model information from specific cells or patterns
            // Adjust these based on your actual Excel structure
            for (int row = 1; row <= Math.Min(10, worksheet.Dimension?.End.Row ?? 0); row++)
            {
                for (int col = 1; col <= Math.Min(10, worksheet.Dimension?.End.Column ?? 0); col++)
                {
                    var cellValue = worksheet.Cells[row, col].Text?.Trim();
                    if (!string.IsNullOrEmpty(cellValue))
                    {
                        // Look for model-related information
                        if (cellValue.ToUpper().Contains("MODEL"))
                        {
                            modelInfo.ModelCode = worksheet.Cells[row, col + 1].Text?.Trim();
                        }
                        else if (cellValue.ToUpper().Contains("NAME"))
                        {
                            modelInfo.ModelName = worksheet.Cells[row, col + 1].Text?.Trim();
                        }
                        else if (cellValue.ToUpper().Contains("YEAR"))
                        {
                            modelInfo.ModelYear = worksheet.Cells[row, col + 1].Text?.Trim();
                        }
                    }
                }
            }

            return modelInfo;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"Error processing model info sheet {worksheet.Name}");
            return null;
        }
    }

    /// <summary>
    /// Process parts data from a worksheet
    /// </summary>
    private List<PartsData> ProcessPartsDataSheet(ExcelWorksheet worksheet, Guid fileUploadId, string createdBy)
    {
        var partsDataList = new List<PartsData>();

        try
        {
            if (worksheet.Dimension == null) return partsDataList;

            // Find header row (look for known column names)
            int headerRow = FindHeaderRow(worksheet);
            if (headerRow == -1) return partsDataList;

            // Get column mappings
            var columnMappings = GetColumnMappings(worksheet, headerRow);

            // Process data rows
            for (int row = headerRow + 1; row <= worksheet.Dimension.End.Row; row++)
            {
                var partsData = CreatePartsDataFromRow(worksheet, row, columnMappings, fileUploadId, createdBy);
                if (partsData != null)
                {
                    partsData.RowNumber = row;
                    partsDataList.Add(partsData);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error processing parts data sheet {worksheet.Name}");
        }

        return partsDataList;
    }

    /// <summary>
    /// Find the header row in the worksheet
    /// </summary>
    private int FindHeaderRow(ExcelWorksheet worksheet)
    {
        for (int row = 1; row <= Math.Min(5, worksheet.Dimension?.End.Row ?? 0); row++)
        {
            var firstCellValue = worksheet.Cells[row, 1].Text?.Trim().ToUpper();
            if (firstCellValue == "KD MODEL CODE" || firstCellValue == "KDMODELCODE")
            {
                return row;
            }
        }
        return 1; // Default to first row if header not found
    }

    /// <summary>
    /// Get column mappings from header row
    /// </summary>
    private Dictionary<string, int> GetColumnMappings(ExcelWorksheet worksheet, int headerRow)
    {
        var mappings = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

        for (int col = 1; col <= worksheet.Dimension?.End.Column; col++)
        {
            var headerValue = worksheet.Cells[headerRow, col].Text?.Trim();
            if (!string.IsNullOrEmpty(headerValue))
            {
                // Normalize header names - remove spaces, commas, and special characters
                var normalizedHeader = headerValue
                    .Replace(" ", "")
                    .Replace(",", "")
                    .Replace("-", "")
                    .Replace("_", "")
                    .Replace("(", "")
                    .Replace(")", "");

                mappings[normalizedHeader] = col;

                // Also add the original header for exact matches
                mappings[headerValue] = col;
            }
        }

        return mappings;
    }

    /// <summary>
    /// Create PartsData object from worksheet row
    /// </summary>
    private PartsData? CreatePartsDataFromRow(ExcelWorksheet worksheet, int row, 
        Dictionary<string, int> columnMappings, Guid fileUploadId, string createdBy)
    {
        try
        {
            // Check if row has any data
            var firstCellValue = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrEmpty(firstCellValue)) return null;

            var partsData = new PartsData
            {
                FileUploadId = fileUploadId,
                SheetName = worksheet.Name,
                CreatedBy = createdBy
            };

            // Map columns based on header mappings
            partsData.KDModelCode = GetCellValue(worksheet, row, columnMappings, "KDModelCode");
            partsData.KDV = GetCellValue(worksheet, row, columnMappings, "KDV");
            partsData.Dealer = GetCellValue(worksheet, row, columnMappings, "Dealer");
            partsData.LVL = GetCellValue(worksheet, row, columnMappings, "LVL");
            partsData.SendType = GetCellValue(worksheet, row, columnMappings, "SendTYPE");
            partsData.KDRefNo = GetCellValue(worksheet, row, columnMappings, "KDREFNO");
            partsData.PartNo = GetCellValue(worksheet, row, columnMappings, "PartNO");
            partsData.InstallLocationComment = GetCellValue(worksheet, row, columnMappings, "InstallLocationComment");
            partsData.QTY = GetCellValue(worksheet, row, columnMappings, "QTY");
            partsData.KDOrderRate = GetCellValue(worksheet, row, columnMappings, "KDOrderRate");
            partsData.SumQTY = GetCellValue(worksheet, row, columnMappings, "SUMQTY");
            partsData.ActualOrderRate = GetCellValue(worksheet, row, columnMappings, "ActualOrderRate");
            partsData.CommentCode = GetCellValue(worksheet, row, columnMappings, "CommentCode");
            partsData.KDColorType = GetCellValue(worksheet, row, columnMappings, "KDColortype");
            partsData.NBRType = GetCellValue(worksheet, row, columnMappings, "NBRType");
            partsData.LocalizationType = GetCellValue(worksheet, row, columnMappings, "LocalizationTYPE");
            partsData.BOMGName = GetCellValue(worksheet, row, columnMappings, "BOMGNAME");
            partsData.BV = GetCellValue(worksheet, row, columnMappings, "BV");

            // Add more mappings as needed...

            return partsData;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"Error creating parts data from row {row}");
            return null;
        }
    }

    /// <summary>
    /// Get cell value by column mapping - tries multiple variations of column names
    /// </summary>
    private string? GetCellValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> columnMappings, string columnName)
    {
        // Try exact match first
        if (columnMappings.TryGetValue(columnName, out int columnIndex))
        {
            return worksheet.Cells[row, columnIndex].Text?.Trim();
        }

        // Try normalized version
        var normalizedColumnName = columnName.Replace(" ", "").Replace(",", "").Replace("-", "").Replace("_", "");
        if (columnMappings.TryGetValue(normalizedColumnName, out columnIndex))
        {
            return worksheet.Cells[row, columnIndex].Text?.Trim();
        }

        // Try common variations
        var variations = new[]
        {
            columnName.Replace(" ", ""),
            columnName.Replace(" ", "_"),
            columnName.Replace(" ", "-"),
            columnName.ToUpper(),
            columnName.ToLower()
        };

        foreach (var variation in variations)
        {
            if (columnMappings.TryGetValue(variation, out columnIndex))
            {
                return worksheet.Cells[row, columnIndex].Text?.Trim();
            }
        }

        return null;
    }

    /// <summary>
    /// Clear all existing KDBOM data
    /// </summary>
    private async Task ClearExistingKDBOMDataAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        _logger.LogInformation("Clearing existing KDBOM data");
        
        // Delete in correct order due to foreign key constraints
        await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.PartsData");
        await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.ModelInfo");
        await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.FileUploads");
        
        _logger.LogInformation("Existing KDBOM data cleared");
    }

    /// <summary>
    /// Get all uploaded KDBOM files
    /// </summary>
    public async Task<List<KDBOMFileInfo>> GetUploadedFilesAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        return await context.KDBOMFileUploads
            .OrderByDescending(f => f.UploadedDate)
            .Select(f => new KDBOMFileInfo
            {
                Id = f.Id,
                FileName = f.FileName,
                OriginalFileName = f.OriginalFileName,
                FileSize = f.FileSize,
                UploadedBy = f.UploadedBy,
                UploadedDate = f.UploadedDate,
                ProcessedDate = f.ProcessedDate,
                ProcessingStatus = f.ProcessingStatus,
                ErrorMessage = f.ErrorMessage,
                RecordCount = f.RecordCount
            })
            .ToListAsync();
    }

    /// <summary>
    /// Get KDBOM parts data with pagination and filtering
    /// </summary>
    public async Task<(List<KDBOMPartsDataDto> Data, int TotalCount)> GetPartsDataAsync(
        int page = 1, int pageSize = 50, string? searchTerm = null, string? sheetFilter = null)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();

        var query = from pd in context.KDBOMPartsData
                    join fu in context.KDBOMFileUploads on pd.FileUploadId equals fu.Id
                    where fu.ProcessingStatus == "Completed"
                    select new KDBOMPartsDataDto
                    {
                        Id = pd.Id,
                        SheetName = pd.SheetName,
                        FileName = fu.FileName,
                        UploadedBy = fu.UploadedBy,
                        UploadedDate = fu.UploadedDate,
                        KDModelCode = pd.KDModelCode ?? "",
                        PartNo = pd.PartNo ?? "",
                        InstallLocationComment = pd.InstallLocationComment ?? "",
                        QTY = pd.QTY ?? "",
                        BOMGName = pd.BOMGName ?? "",
                        Dealer = pd.Dealer ?? "",
                        CCCode = pd.CCCode ?? ""
                    };

        // Apply filters
        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(x => 
                x.KDModelCode.Contains(searchTerm) ||
                x.PartNo.Contains(searchTerm) ||
                x.InstallLocationComment.Contains(searchTerm) ||
                x.BOMGName.Contains(searchTerm));
        }

        if (!string.IsNullOrEmpty(sheetFilter))
        {
            query = query.Where(x => x.SheetName == sheetFilter);
        }

        var totalCount = await query.CountAsync();
        var data = await query
            .OrderBy(x => x.FileName)
            .ThenBy(x => x.SheetName)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (data, totalCount);
    }

    /// <summary>
    /// Get distinct sheet names for filtering
    /// </summary>
    public async Task<List<string>> GetDistinctSheetNamesAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        return await context.KDBOMPartsData
            .Select(pd => pd.SheetName)
            .Distinct()
            .OrderBy(s => s)
            .ToListAsync();
    }
}
