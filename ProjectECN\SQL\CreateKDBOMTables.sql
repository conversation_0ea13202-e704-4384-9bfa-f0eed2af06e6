-- KDBOM Database Tables Creation Script
-- This script creates tables for importing KDBOM Excel files with multiple sheets

-- Create schema for KDBOM data
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'kdbom')
BEGIN
    EXEC('CREATE SCHEMA kdbom')
END
GO

-- Table to store file upload information
CREATE TABLE kdbom.FileUploads (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    FileSize BIGINT NOT NULL,
    UploadedBy NVARCHAR(100) NOT NULL,
    UploadedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ProcessedDate DATETIME2 NULL,
    ProcessingStatus NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Processing, Completed, Failed
    ErrorMessage NVARCHAR(MAX) NULL,
    RecordCount INT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100) NOT NULL,
    ModifiedDate DATETIME2 NULL,
    ModifiedBy NVARCHAR(100) NULL
)
GO

-- Table to store model information (from first sheet)
CREATE TABLE kdbom.ModelInfo (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    FileUploadId UNIQUEIDENTIFIER NOT NULL,
    SheetName NVARCHAR(100) NOT NULL,
    ModelCode NVARCHAR(50) NULL,
    ModelName NVARCHAR(255) NULL,
    ModelDescription NVARCHAR(500) NULL,
    ModelYear NVARCHAR(10) NULL,
    ModelType NVARCHAR(50) NULL,
    AdditionalInfo NVARCHAR(MAX) NULL, -- JSON for any additional model data
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100) NOT NULL,
    FOREIGN KEY (FileUploadId) REFERENCES kdbom.FileUploads(Id) ON DELETE CASCADE
)
GO

-- Table to store parts list data (from subsequent sheets)
CREATE TABLE kdbom.PartsData (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    FileUploadId UNIQUEIDENTIFIER NOT NULL,
    SheetName NVARCHAR(100) NOT NULL,
    RowNumber INT NOT NULL,
    
    -- Main columns from the CSV sample
    KDModelCode NVARCHAR(50) NULL,
    KDV NVARCHAR(10) NULL,
    Dealer NVARCHAR(50) NULL,
    LVL NVARCHAR(10) NULL,
    SendType NVARCHAR(10) NULL,
    KDRefNo NVARCHAR(50) NULL,
    PartNo NVARCHAR(50) NULL,
    InstallLocationComment NVARCHAR(255) NULL,
    QTY NVARCHAR(20) NULL,
    KDOrderRate NVARCHAR(20) NULL,
    SumQTY NVARCHAR(20) NULL,
    ActualOrderRate NVARCHAR(20) NULL,
    CommentCode NVARCHAR(20) NULL,
    KDColorType NVARCHAR(20) NULL,
    NBRType NVARCHAR(20) NULL,
    LocalizationType NVARCHAR(20) NULL,
    BOMGName NVARCHAR(50) NULL,
    BV NVARCHAR(50) NULL,
    AplySEcnNo NVARCHAR(50) NULL,
    AplySEcnRglrDate NVARCHAR(20) NULL,
    ChangeReasonSummaryType NVARCHAR(100) NULL,
    ChangeReasonDetailType NVARCHAR(100) NULL,
    AplyEEcnNo NVARCHAR(50) NULL,
    AplyEEcnRglrDate NVARCHAR(20) NULL,
    KDVChild NVARCHAR(20) NULL,
    SubDealer NVARCHAR(50) NULL,
    SupplyArea NVARCHAR(50) NULL,
    CartonType NVARCHAR(50) NULL,
    ResultType NVARCHAR(50) NULL,
    EngineTransmissionType NVARCHAR(50) NULL,
    EngineTransmissionPartType NVARCHAR(50) NULL,
    SEL1 NVARCHAR(50) NULL,
    SEL2 NVARCHAR(50) NULL,
    SEL3 NVARCHAR(50) NULL,
    StrSel NVARCHAR(50) NULL,
    StrSelRsnCd NVARCHAR(50) NULL,
    AAR NVARCHAR(50) NULL,
    PartsTP NVARCHAR(50) NULL,
    CCCodeProd NVARCHAR(50) NULL,
    PltCdPLoc NVARCHAR(50) NULL,
    LineCdPLoc NVARCHAR(50) NULL,
    CCCodeTo NVARCHAR(50) NULL,
    CCCodeFrom NVARCHAR(50) NULL,
    RtgCdFr NVARCHAR(50) NULL,
    CCCode NVARCHAR(50) NULL,
    KDSpsntFlg NVARCHAR(20) NULL,
    TrdrFlag NVARCHAR(20) NULL,
    PBOMPartNumberParent NVARCHAR(50) NULL,
    DBLY NVARCHAR(20) NULL,
    KDDwg NVARCHAR(50) NULL,
    SendStop NVARCHAR(20) NULL,
    CCCodeFromPT NVARCHAR(50) NULL,
    CCCodeToPT NVARCHAR(50) NULL,
    PTOdrNo NVARCHAR(50) NULL,
    PurType NVARCHAR(20) NULL,
    PSLCd NVARCHAR(50) NULL,
    PlanCode NVARCHAR(50) NULL,
    Shop NVARCHAR(50) NULL,
    DualOrderNo NVARCHAR(50) NULL,
    DualOrderType NVARCHAR(50) NULL,
    ProcessNo NVARCHAR(50) NULL,
    ProcessRate NVARCHAR(50) NULL,
    ProcurementType NVARCHAR(50) NULL,
    SupplyProcessType NVARCHAR(50) NULL,
    DeliveryLocationProcessCodeOut NVARCHAR(50) NULL,
    DeliveryLocationInProcessCode NVARCHAR(50) NULL,
    DeliveryLocationInPlantCode NVARCHAR(50) NULL,
    DeliveryProcessCode NVARCHAR(50) NULL,
    DeliveryPlantCode NVARCHAR(50) NULL,
    ColorRelatedType1digit NVARCHAR(20) NULL,
    ProcessSeqId NVARCHAR(50) NULL,
    DeliveryType NVARCHAR(50) NULL,
    LeadTime NVARCHAR(50) NULL,
    MaterialPartNo NVARCHAR(50) NULL,
    AplySDate NVARCHAR(20) NULL,
    AplyEDate NVARCHAR(20) NULL,
    OutputSEQ NVARCHAR(20) NULL,
    
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100) NOT NULL,
    FOREIGN KEY (FileUploadId) REFERENCES kdbom.FileUploads(Id) ON DELETE CASCADE
)
GO

-- Create indexes for better performance
CREATE INDEX IX_FileUploads_UploadedDate ON kdbom.FileUploads(UploadedDate DESC)
GO

CREATE INDEX IX_FileUploads_Status ON kdbom.FileUploads(ProcessingStatus)
GO

CREATE INDEX IX_ModelInfo_FileUploadId ON kdbom.ModelInfo(FileUploadId)
GO

CREATE INDEX IX_PartsData_FileUploadId ON kdbom.PartsData(FileUploadId)
GO

CREATE INDEX IX_PartsData_KDModelCode ON kdbom.PartsData(KDModelCode)
GO

CREATE INDEX IX_PartsData_PartNo ON kdbom.PartsData(PartNo)
GO

CREATE INDEX IX_PartsData_SheetName ON kdbom.PartsData(SheetName)
GO

-- Create a view for easy data retrieval
CREATE VIEW kdbom.vw_PartsDataWithFileInfo AS
SELECT 
    pd.*,
    fu.FileName,
    fu.OriginalFileName,
    fu.UploadedBy,
    fu.UploadedDate,
    fu.ProcessedDate
FROM kdbom.PartsData pd
INNER JOIN kdbom.FileUploads fu ON pd.FileUploadId = fu.Id
WHERE fu.ProcessingStatus = 'Completed'
GO

PRINT 'KDBOM tables created successfully!'
