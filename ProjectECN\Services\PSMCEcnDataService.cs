using ProjectECN.DTO;
using System.Text.Json;

namespace ProjectECN.Services
{
    public class PSMCEcnDataService
    {
        private readonly ILogger<PSMCEcnDataService> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly string _dataFilePath;

        public PSMCEcnDataService(ILogger<PSMCEcnDataService> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _environment = environment;
            _dataFilePath = Path.Combine(_environment.WebRootPath, "data", "psmc-ecns.json");
            
            // Ensure data directory exists
            var dataDir = Path.GetDirectoryName(_dataFilePath);
            if (!Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir!);
            }
        }

        /// <summary>
        /// Save PSMC ECN data to persistent storage
        /// </summary>
        public async Task SavePSMCEcnDataAsync(List<PSMCEcnDto> psmcEcns)
        {
            try
            {
                var existingData = await LoadPSMCEcnDataAsync();
                
                // Merge new data with existing data (avoid duplicates based on ECN number and process date)
                foreach (var newEcn in psmcEcns)
                {
                    var existing = existingData.FirstOrDefault(e => 
                        e.EcnNumber == newEcn.EcnNumber && 
                        e.ProcessDate == newEcn.ProcessDate);
                    
                    if (existing != null)
                    {
                        // Update existing record
                        var index = existingData.IndexOf(existing);
                        existingData[index] = newEcn;
                        _logger.LogInformation($"Updated existing PSMC ECN record: {newEcn.EcnNumber}");
                    }
                    else
                    {
                        // Add new record
                        existingData.Add(newEcn);
                        _logger.LogInformation($"Added new PSMC ECN record: {newEcn.EcnNumber}");
                    }
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var json = JsonSerializer.Serialize(existingData, options);
                await File.WriteAllTextAsync(_dataFilePath, json);
                
                _logger.LogInformation($"Saved {existingData.Count} PSMC ECN records to {_dataFilePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving PSMC ECN data");
                throw;
            }
        }

        /// <summary>
        /// Load all PSMC ECN data from persistent storage
        /// </summary>
        public async Task<List<PSMCEcnDto>> LoadPSMCEcnDataAsync()
        {
            try
            {
                if (!File.Exists(_dataFilePath))
                {
                    _logger.LogInformation("PSMC ECN data file does not exist, returning empty list");
                    return new List<PSMCEcnDto>();
                }

                var json = await File.ReadAllTextAsync(_dataFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var data = JsonSerializer.Deserialize<List<PSMCEcnDto>>(json, options) ?? new List<PSMCEcnDto>();
                _logger.LogInformation($"Loaded {data.Count} PSMC ECN records from {_dataFilePath}");
                
                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading PSMC ECN data");
                return new List<PSMCEcnDto>();
            }
        }

        /// <summary>
        /// Get PSMC ECNs that qualify for processing (OTHERS checkbox marked AND PSMC text present)
        /// </summary>
        public async Task<List<PSMCEcnDto>> GetQualifiedPSMCEcnsAsync()
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.Where(e => e.QualifiesForPSMC).ToList();
        }

        /// <summary>
        /// Get PSMC ECNs filtered by date range
        /// </summary>
        public async Task<List<PSMCEcnDto>> GetPSMCEcnsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.Where(e => 
                e.ProcessDateTime >= startDate && 
                e.ProcessDateTime <= endDate &&
                e.QualifiesForPSMC).ToList();
        }

        /// <summary>
        /// Get PSMC ECNs by vehicle type (2W or 4W)
        /// </summary>
        public async Task<List<PSMCEcnDto>> GetPSMCEcnsByVehicleTypeAsync(string vehicleType)
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.Where(e => 
                e.VehicleType.Equals(vehicleType, StringComparison.OrdinalIgnoreCase) &&
                e.QualifiesForPSMC).ToList();
        }

        /// <summary>
        /// Get processing summary statistics
        /// </summary>
        public async Task<PSMCProcessingSummary> GetProcessingSummaryAsync()
        {
            var allData = await LoadPSMCEcnDataAsync();
            var qualified = allData.Where(e => e.QualifiesForPSMC).ToList();

            return new PSMCProcessingSummary
            {
                TotalProcessed = allData.Count,
                PSMCQualified = qualified.Count,
                OthersCheckboxMarked = allData.Count(e => e.IsOthersCheckboxMarked),
                PSMCTextDetected = allData.Count(e => e.HasPSMCText),
                QualifiedECNs = qualified,
                ProcessingStarted = allData.Any() ? allData.Min(e => e.ProcessedAt) : DateTime.UtcNow,
                ProcessingCompleted = allData.Any() ? allData.Max(e => e.ProcessedAt) : DateTime.UtcNow
            };
        }

        /// <summary>
        /// Search PSMC ECNs by ECN number
        /// </summary>
        public async Task<List<PSMCEcnDto>> SearchPSMCEcnsByNumberAsync(string ecnNumber)
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.Where(e => 
                e.EcnNumber.Contains(ecnNumber, StringComparison.OrdinalIgnoreCase) &&
                e.QualifiesForPSMC).ToList();
        }

        /// <summary>
        /// Get latest PSMC ECNs (most recently processed)
        /// </summary>
        public async Task<List<PSMCEcnDto>> GetLatestPSMCEcnsAsync(int count = 10)
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.Where(e => e.QualifiesForPSMC)
                         .OrderByDescending(e => e.ProcessedAt)
                         .Take(count)
                         .ToList();
        }

        /// <summary>
        /// Export PSMC ECN data to CSV format
        /// </summary>
        public async Task<string> ExportPSMCEcnsToCsvAsync()
        {
            var qualifiedEcns = await GetQualifiedPSMCEcnsAsync();
            
            var csv = new List<string>
            {
                "ECN Number,File Name,Process Date,Vehicle Type,Has PSMC Text,Others Checkbox Marked,OCR Confidence,Processed At,Processing Notes"
            };

            foreach (var ecn in qualifiedEcns)
            {
                var notes = string.Join("; ", ecn.ProcessingNotes);
                csv.Add($"{ecn.EcnNumber},{ecn.FileName},{ecn.ProcessDate},{ecn.VehicleType},{ecn.HasPSMCText},{ecn.IsOthersCheckboxMarked},{ecn.OcrConfidence:F2},{ecn.ProcessedAt:yyyy-MM-dd HH:mm:ss},\"{notes}\"");
            }

            return string.Join(Environment.NewLine, csv);
        }

        /// <summary>
        /// Clear all PSMC ECN data
        /// </summary>
        public Task ClearAllDataAsync()
        {
            try
            {
                if (File.Exists(_dataFilePath))
                {
                    File.Delete(_dataFilePath);
                    _logger.LogInformation("Cleared all PSMC ECN data");
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing PSMC ECN data");
                throw;
            }
        }

        /// <summary>
        /// Get ECN details by ECN number and process date
        /// </summary>
        public async Task<PSMCEcnDto?> GetEcnDetailsAsync(string ecnNumber, string processDate)
        {
            var allData = await LoadPSMCEcnDataAsync();
            return allData.FirstOrDefault(e => 
                e.EcnNumber == ecnNumber && 
                e.ProcessDate == processDate);
        }
    }
}
