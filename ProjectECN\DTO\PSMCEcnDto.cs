using System.ComponentModel.DataAnnotations;

namespace ProjectECN.DTO
{
    /// <summary>
    /// Data Transfer Object for ECN records specifically related to PSMC factory
    /// Contains information extracted from factory images where OTHERS checkbox is marked and PSMC text is present
    /// </summary>
    public class PSMCEcnDto
    {
        /// <summary>
        /// ECN Number (e.g., 52U-0270)
        /// </summary>
        [Required]
        public string EcnNumber { get; set; } = string.Empty;

        /// <summary>
        /// Full file name of the original TIFF file
        /// </summary>
        [Required]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Date extracted from the folder structure (YYYYMMDD)
        /// </summary>
        [Required]
        public string ProcessDate { get; set; } = string.Empty;

        /// <summary>
        /// Parsed date from ProcessDate
        /// </summary>
        public DateTime ProcessDateTime => DateTime.TryParseExact(ProcessDate, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var date) ? date : DateTime.MinValue;

        /// <summary>
        /// Vehicle type (2W or 4W)
        /// </summary>
        [Required]
        public string VehicleType { get; set; } = string.Empty;

        /// <summary>
        /// Full path to the original TIFF file
        /// </summary>
        public string OriginalFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Path to the extracted factory image (PNG)
        /// </summary>
        public string FactoryImagePath { get; set; } = string.Empty;

        /// <summary>
        /// Path to the extracted description image (PNG)
        /// </summary>
        public string DescriptionImagePath { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the OTHERS checkbox was detected as checked/marked
        /// </summary>
        public bool IsOthersCheckboxMarked { get; set; }

        /// <summary>
        /// Indicates if PSMC text was found in the factory image
        /// </summary>
        public bool HasPSMCText { get; set; }

        /// <summary>
        /// Raw OCR text extracted from the factory image
        /// </summary>
        public string FactoryOcrText { get; set; } = string.Empty;

        /// <summary>
        /// Confidence score of the OCR text recognition (0-100)
        /// </summary>
        public float OcrConfidence { get; set; }

        /// <summary>
        /// List of detected factory names/labels from the OCR
        /// </summary>
        public List<string> DetectedFactories { get; set; } = new();

        /// <summary>
        /// Checkbox detection results with positions and status
        /// </summary>
        public List<CheckboxDetectionResult> CheckboxResults { get; set; } = new();

        /// <summary>
        /// Timestamp when this record was processed
        /// </summary>
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Any processing errors or warnings
        /// </summary>
        public List<string> ProcessingNotes { get; set; } = new();

        /// <summary>
        /// Indicates if this ECN qualifies for PSMC processing
        /// (OTHERS checkbox marked AND PSMC text present)
        /// </summary>
        public bool QualifiesForPSMC => IsOthersCheckboxMarked && HasPSMCText;

        /// <summary>
        /// Additional metadata extracted from the ECN
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Represents the result of checkbox detection in factory images
    /// </summary>
    public class CheckboxDetectionResult
    {
        /// <summary>
        /// Label associated with the checkbox (e.g., "OTHERS", "PSMC", etc.)
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// X coordinate of the checkbox
        /// </summary>
        public int X { get; set; }

        /// <summary>
        /// Y coordinate of the checkbox
        /// </summary>
        public int Y { get; set; }

        /// <summary>
        /// Width of the checkbox area
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// Height of the checkbox area
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// Indicates if the checkbox appears to be checked/marked
        /// </summary>
        public bool IsChecked { get; set; }

        /// <summary>
        /// Confidence score of the checkbox detection (0-100)
        /// </summary>
        public float Confidence { get; set; }

        /// <summary>
        /// Method used for detection (e.g., "PixelDensity", "ContourDetection")
        /// </summary>
        public string DetectionMethod { get; set; } = string.Empty;
    }

    /// <summary>
    /// Summary statistics for PSMC ECN processing
    /// </summary>
    public class PSMCProcessingSummary
    {
        /// <summary>
        /// Total number of ECNs processed
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// Number of ECNs that qualify for PSMC (OTHERS checked + PSMC text)
        /// </summary>
        public int PSMCQualified { get; set; }

        /// <summary>
        /// Number of ECNs with OTHERS checkbox marked
        /// </summary>
        public int OthersCheckboxMarked { get; set; }

        /// <summary>
        /// Number of ECNs with PSMC text detected
        /// </summary>
        public int PSMCTextDetected { get; set; }

        /// <summary>
        /// Processing start time
        /// </summary>
        public DateTime ProcessingStarted { get; set; }

        /// <summary>
        /// Processing end time
        /// </summary>
        public DateTime ProcessingCompleted { get; set; }

        /// <summary>
        /// Total processing duration
        /// </summary>
        public TimeSpan ProcessingDuration => ProcessingCompleted - ProcessingStarted;

        /// <summary>
        /// List of ECNs that qualify for PSMC processing
        /// </summary>
        public List<PSMCEcnDto> QualifiedECNs { get; set; } = new();

        /// <summary>
        /// Any errors encountered during processing
        /// </summary>
        public List<string> ProcessingErrors { get; set; } = new();
    }
}
