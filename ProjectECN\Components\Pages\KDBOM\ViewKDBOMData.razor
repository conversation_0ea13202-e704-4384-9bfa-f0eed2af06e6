@page "/kdbom/data"
@rendermode InteractiveServer
@inject KDBOMService KDBOMService
@inject IJSRuntime JSRuntime
@using ProjectECN.Services
@using ProjectECN.DTO.KDBOM
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs

<div class="kdbom-data-container">
    <div class="page-header">
        <h2>📊 KDBOM Data View</h2>
        <p class="page-description">View and search imported KDBOM parts data</p>
    </div>

    <div class="data-card">
        <!-- Controls Section -->
        <div class="controls-section">
            <div class="search-controls">
                <SfTextBox @bind-Value="@_searchTerm" 
                          Placeholder="Search by Model Code, Part No, or Description..."
                          ShowClearButton="true"
                          @onkeyup="HandleSearchKeyUp"
                          CssClass="search-input">
                </SfTextBox>
                <button type="button" @onclick="HandleSearch" class="search-button">
                    🔍 Search
                </button>
            </div>
            
            <div class="filter-controls">
                <SfDropDownList @bind-Value="@_selectedSheet" 
                               DataSource="@_sheetNames" 
                               Placeholder="Filter by Sheet"
                               AllowClear="true"
                               @onchange="HandleSheetFilterChange"
                               CssClass="sheet-filter">
                </SfDropDownList>
                
                <button type="button" @onclick="RefreshData" class="refresh-button">
                    🔄 Refresh
                </button>
                
                <button type="button" @onclick="NavigateToImport" class="import-button">
                    ⬆️ Import New Files
                </button>
            </div>
        </div>

        <!-- Summary Section -->
        @if (_totalRecords > 0)
        {
            <div class="summary-section">
                <div class="summary-item">
                    <span class="summary-label">Total Records:</span>
                    <span class="summary-value">@_totalRecords.ToString("N0")</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Showing:</span>
                    <span class="summary-value">@_partsData.Count.ToString("N0")</span>
                </div>
                @if (!string.IsNullOrEmpty(_selectedSheet))
                {
                    <div class="summary-item">
                        <span class="summary-label">Sheet:</span>
                        <span class="summary-value">@_selectedSheet</span>
                    </div>
                }
            </div>
        }

        <!-- Data Grid -->
        @if (_isLoading)
        {
            <div class="loading-section">
                <div class="loading-spinner"></div>
                <p>Loading KDBOM data...</p>
            </div>
        }
        else if (_partsData.Any())
        {
            <div class="grid-section">
                <SfGrid DataSource="@_partsData" 
                        AllowPaging="true" 
                        AllowSorting="true" 
                        AllowFiltering="true"
                        AllowResizing="true"
                        AllowReordering="true"
                        PageSize="50"
                        Height="600px"
                        GridLines="GridLine.Both"
                        CssClass="kdbom-grid">
                    
                    <GridPageSettings PageSize="50" PageSizes="@(new int[] {25, 50, 100, 200})"></GridPageSettings>
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>
                    
                    <GridColumns>
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.FileName)" 
                                   HeaderText="File Name" 
                                   Width="150" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.SheetName)" 
                                   HeaderText="Sheet" 
                                   Width="120" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.KDModelCode)" 
                                   HeaderText="Model Code" 
                                   Width="120" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.PartNo)" 
                                   HeaderText="Part No" 
                                   Width="150" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.InstallLocationComment)" 
                                   HeaderText="Install Location" 
                                   Width="200" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.QTY)" 
                                   HeaderText="QTY" 
                                   Width="80" 
                                   TextAlign="TextAlign.Center">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.BOMGName)" 
                                   HeaderText="BOM Name" 
                                   Width="120" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.Dealer)" 
                                   HeaderText="Dealer" 
                                   Width="100" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.CCCode)" 
                                   HeaderText="CC Code" 
                                   Width="100" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.UploadedBy)" 
                                   HeaderText="Uploaded By" 
                                   Width="120" 
                                   TextAlign="TextAlign.Left">
                        </GridColumn>
                        
                        <GridColumn Field="@nameof(KDBOMPartsDataDto.FormattedUploadDate)" 
                                   HeaderText="Upload Date" 
                                   Width="120" 
                                   TextAlign="TextAlign.Center">
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        }
        else
        {
            <div class="no-data-section">
                <div class="no-data-icon">📋</div>
                <h3>No KDBOM Data Found</h3>
                <p>No KDBOM data has been imported yet or no data matches your search criteria.</p>
                <button type="button" @onclick="NavigateToImport" class="import-button">
                    ⬆️ Import KDBOM Files
                </button>
            </div>
        }

        <!-- File List Section -->
        @if (_uploadedFiles.Any())
        {
            <div class="files-section">
                <h3>📁 Uploaded Files</h3>
                <div class="files-grid">
                    @foreach (var file in _uploadedFiles.Take(10))
                    {
                        <div class="file-card @(file.ProcessingStatus.ToLower())">
                            <div class="file-header">
                                <strong>@file.OriginalFileName</strong>
                                <span class="status-badge @file.ProcessingStatus.ToLower()">@file.ProcessingStatus</span>
                            </div>
                            <div class="file-details">
                                <div class="file-detail">
                                    <span class="label">Size:</span>
                                    <span class="value">@file.FormattedFileSize</span>
                                </div>
                                <div class="file-detail">
                                    <span class="label">Uploaded:</span>
                                    <span class="value">@file.FormattedUploadDate</span>
                                </div>
                                <div class="file-detail">
                                    <span class="label">By:</span>
                                    <span class="value">@file.UploadedBy</span>
                                </div>
                                @if (file.RecordCount.HasValue)
                                {
                                    <div class="file-detail">
                                        <span class="label">Records:</span>
                                        <span class="value">@file.RecordCount.Value.ToString("N0")</span>
                                    </div>
                                }
                            </div>
                            @if (!string.IsNullOrEmpty(file.ErrorMessage))
                            {
                                <div class="error-message">
                                    <small>@file.ErrorMessage</small>
                                </div>
                            }
                        </div>
                    }
                </div>
                @if (_uploadedFiles.Count > 10)
                {
                    <p class="more-files">... and @(_uploadedFiles.Count - 10) more files</p>
                }
            </div>
        }
    </div>
</div>

@code {
    private List<KDBOMPartsDataDto> _partsData = new();
    private List<KDBOMFileInfo> _uploadedFiles = new();
    private List<string> _sheetNames = new();
    private string _searchTerm = string.Empty;
    private string? _selectedSheet;
    private bool _isLoading = true;
    private int _totalRecords = 0;
    private int _currentPage = 1;
    private int _pageSize = 50;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        _isLoading = true;
        StateHasChanged();

        try
        {
            // Load parts data
            var (data, totalCount) = await KDBOMService.GetPartsDataAsync(_currentPage, _pageSize, _searchTerm, _selectedSheet);
            _partsData = data;
            _totalRecords = totalCount;

            // Load uploaded files
            _uploadedFiles = await KDBOMService.GetUploadedFilesAsync();

            // Load sheet names for filtering
            _sheetNames = await KDBOMService.GetDistinctSheetNamesAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading KDBOM data:", ex.Message);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleSearch()
    {
        _currentPage = 1;
        await LoadDataAsync();
    }

    private async Task HandleSearchKeyUp(Microsoft.AspNetCore.Components.Web.KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await HandleSearch();
        }
    }

    private async Task HandleSheetFilterChange()
    {
        _currentPage = 1;
        await LoadDataAsync();
    }

    private async Task RefreshData()
    {
        await LoadDataAsync();
    }

    private async Task NavigateToImport()
    {
        await JSRuntime.InvokeVoidAsync("open", "/kdbom/import", "_self");
    }
}

<style>
    .kdbom-data-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .page-header h2 {
        color: var(--accent-fill-rest);
        margin: 0 0 0.5rem 0;
        font-size: 1.8rem;
    }

    .page-description {
        color: var(--neutral-foreground-rest);
        margin: 0;
        font-size: 0.95rem;
    }

    .data-card {
        background: var(--neutral-layer-1);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .controls-section {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--neutral-stroke-rest);
    }

    .search-controls {
        display: flex;
        gap: 0.5rem;
        flex: 1;
        min-width: 300px;
    }

    .search-input {
        flex: 1;
    }

    .search-button {
        background: var(--accent-fill-rest);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        white-space: nowrap;
        transition: background-color 0.2s;
    }

    .search-button:hover {
        background: var(--accent-fill-hover);
    }

    .filter-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .sheet-filter {
        min-width: 150px;
    }

    .refresh-button, .import-button {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
        border: 1px solid var(--neutral-stroke-rest);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        white-space: nowrap;
        transition: background-color 0.2s;
    }

    .refresh-button:hover, .import-button:hover {
        background: var(--neutral-fill-hover);
    }

    .import-button {
        background: var(--accent-fill-rest);
        color: white;
        border: none;
    }

    .import-button:hover {
        background: var(--accent-fill-hover);
    }

    .summary-section {
        display: flex;
        gap: 2rem;
        margin-bottom: 1rem;
        padding: 0.8rem;
        background: var(--neutral-layer-2);
        border-radius: 4px;
        flex-wrap: wrap;
    }

    .summary-item {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .summary-label {
        color: var(--neutral-foreground-hint);
        font-size: 0.9rem;
    }

    .summary-value {
        color: var(--accent-fill-rest);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .loading-section {
        text-align: center;
        padding: 3rem 1rem;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--neutral-stroke-rest);
        border-top: 4px solid var(--accent-fill-rest);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem auto;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-section p {
        color: var(--neutral-foreground-hint);
        margin: 0;
    }

    .grid-section {
        margin-bottom: 2rem;
    }

    .kdbom-grid {
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 4px;
    }

    .no-data-section {
        text-align: center;
        padding: 3rem 1rem;
    }

    .no-data-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .no-data-section h3 {
        color: var(--neutral-foreground-rest);
        margin: 0 0 0.5rem 0;
        font-size: 1.3rem;
    }

    .no-data-section p {
        color: var(--neutral-foreground-hint);
        margin: 0 0 1.5rem 0;
        font-size: 0.95rem;
    }

    .files-section {
        border-top: 1px solid var(--neutral-stroke-rest);
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }

    .files-section h3 {
        color: var(--neutral-foreground-rest);
        margin: 0 0 1rem 0;
        font-size: 1.2rem;
    }

    .files-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .file-card {
        background: var(--neutral-layer-2);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 6px;
        padding: 1rem;
        transition: box-shadow 0.2s;
    }

    .file-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .file-card.completed {
        border-left: 4px solid var(--fill-color-success);
    }

    .file-card.failed {
        border-left: 4px solid var(--fill-color-critical);
    }

    .file-card.processing {
        border-left: 4px solid var(--fill-color-warning);
    }

    .file-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.8rem;
    }

    .file-header strong {
        color: var(--neutral-foreground-rest);
        font-size: 0.95rem;
        flex: 1;
        margin-right: 0.5rem;
        word-break: break-word;
    }

    .status-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        white-space: nowrap;
    }

    .status-badge.completed {
        background: var(--fill-color-success);
        color: var(--text-color-success);
    }

    .status-badge.failed {
        background: var(--fill-color-critical);
        color: var(--text-color-critical);
    }

    .status-badge.processing {
        background: var(--fill-color-warning);
        color: var(--text-color-warning);
    }

    .status-badge.pending {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
    }

    .file-details {
        display: flex;
        flex-direction: column;
        gap: 0.3rem;
    }

    .file-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.85rem;
    }

    .file-detail .label {
        color: var(--neutral-foreground-hint);
    }

    .file-detail .value {
        color: var(--neutral-foreground-rest);
        font-weight: 500;
    }

    .error-message {
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: var(--fill-color-critical);
        border-radius: 4px;
    }

    .error-message small {
        color: var(--text-color-critical);
        font-size: 0.8rem;
    }

    .more-files {
        text-align: center;
        color: var(--neutral-foreground-hint);
        font-style: italic;
        margin: 0;
        font-size: 0.9rem;
    }

    @@media (max-width: 768px) {
        .kdbom-data-container {
            padding: 0.5rem;
        }

        .data-card {
            padding: 1rem;
        }

        .controls-section {
            flex-direction: column;
        }

        .search-controls {
            min-width: auto;
        }

        .filter-controls {
            justify-content: stretch;
        }

        .filter-controls > * {
            flex: 1;
        }

        .summary-section {
            flex-direction: column;
            gap: 0.5rem;
        }

        .files-grid {
            grid-template-columns: 1fr;
        }

        .file-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
