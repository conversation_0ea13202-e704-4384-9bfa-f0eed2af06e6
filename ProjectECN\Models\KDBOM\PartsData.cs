using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectECN.Models.KDBOM;

[Table("PartsData", Schema = "kdbom")]
public class PartsData
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid FileUploadId { get; set; }

    [Required]
    [MaxLength(100)]
    public string SheetName { get; set; } = string.Empty;

    public int RowNumber { get; set; }

    // Main columns from the CSV sample
    [MaxLength(50)]
    public string? KDModelCode { get; set; }

    [MaxLength(10)]
    public string? KDV { get; set; }

    [MaxLength(50)]
    public string? Dealer { get; set; }

    [MaxLength(10)]
    public string? LVL { get; set; }

    [MaxLength(10)]
    public string? SendType { get; set; }

    [MaxLength(50)]
    public string? KDRefNo { get; set; }

    [MaxLength(50)]
    public string? PartNo { get; set; }

    [MaxLength(255)]
    public string? InstallLocationComment { get; set; }

    [MaxLength(20)]
    public string? QTY { get; set; }

    [MaxLength(20)]
    public string? KDOrderRate { get; set; }

    [MaxLength(20)]
    public string? SumQTY { get; set; }

    [MaxLength(20)]
    public string? ActualOrderRate { get; set; }

    [MaxLength(20)]
    public string? CommentCode { get; set; }

    [MaxLength(20)]
    public string? KDColorType { get; set; }

    [MaxLength(20)]
    public string? NBRType { get; set; }

    [MaxLength(20)]
    public string? LocalizationType { get; set; }

    [MaxLength(50)]
    public string? BOMGName { get; set; }

    [MaxLength(50)]
    public string? BV { get; set; }

    [MaxLength(50)]
    public string? AplySEcnNo { get; set; }

    [MaxLength(20)]
    public string? AplySEcnRglrDate { get; set; }

    [MaxLength(100)]
    public string? ChangeReasonSummaryType { get; set; }

    [MaxLength(100)]
    public string? ChangeReasonDetailType { get; set; }

    [MaxLength(50)]
    public string? AplyEEcnNo { get; set; }

    [MaxLength(20)]
    public string? AplyEEcnRglrDate { get; set; }

    [MaxLength(20)]
    public string? KDVChild { get; set; }

    [MaxLength(50)]
    public string? SubDealer { get; set; }

    [MaxLength(50)]
    public string? SupplyArea { get; set; }

    [MaxLength(50)]
    public string? CartonType { get; set; }

    [MaxLength(50)]
    public string? ResultType { get; set; }

    [MaxLength(50)]
    public string? EngineTransmissionType { get; set; }

    [MaxLength(50)]
    public string? EngineTransmissionPartType { get; set; }

    [MaxLength(50)]
    public string? SEL1 { get; set; }

    [MaxLength(50)]
    public string? SEL2 { get; set; }

    [MaxLength(50)]
    public string? SEL3 { get; set; }

    [MaxLength(50)]
    public string? StrSel { get; set; }

    [MaxLength(50)]
    public string? StrSelRsnCd { get; set; }

    [MaxLength(50)]
    public string? AAR { get; set; }

    [MaxLength(50)]
    public string? PartsTP { get; set; }

    [MaxLength(50)]
    public string? CCCodeProd { get; set; }

    [MaxLength(50)]
    public string? PltCdPLoc { get; set; }

    [MaxLength(50)]
    public string? LineCdPLoc { get; set; }

    [MaxLength(50)]
    public string? CCCodeTo { get; set; }

    [MaxLength(50)]
    public string? CCCodeFrom { get; set; }

    [MaxLength(50)]
    public string? RtgCdFr { get; set; }

    [MaxLength(50)]
    public string? CCCode { get; set; }

    [MaxLength(20)]
    public string? KDSpsntFlg { get; set; }

    [MaxLength(20)]
    public string? TrdrFlag { get; set; }

    [MaxLength(50)]
    public string? PBOMPartNumberParent { get; set; }

    [MaxLength(20)]
    public string? DBLY { get; set; }

    [MaxLength(50)]
    public string? KDDwg { get; set; }

    [MaxLength(20)]
    public string? SendStop { get; set; }

    [MaxLength(50)]
    public string? CCCodeFromPT { get; set; }

    [MaxLength(50)]
    public string? CCCodeToPT { get; set; }

    [MaxLength(50)]
    public string? PTOdrNo { get; set; }

    [MaxLength(20)]
    public string? PurType { get; set; }

    [MaxLength(50)]
    public string? PSLCd { get; set; }

    [MaxLength(50)]
    public string? PlanCode { get; set; }

    [MaxLength(50)]
    public string? Shop { get; set; }

    [MaxLength(50)]
    public string? DualOrderNo { get; set; }

    [MaxLength(50)]
    public string? DualOrderType { get; set; }

    [MaxLength(50)]
    public string? ProcessNo { get; set; }

    [MaxLength(50)]
    public string? ProcessRate { get; set; }

    [MaxLength(50)]
    public string? ProcurementType { get; set; }

    [MaxLength(50)]
    public string? SupplyProcessType { get; set; }

    [MaxLength(50)]
    public string? DeliveryLocationProcessCodeOut { get; set; }

    [MaxLength(50)]
    public string? DeliveryLocationInProcessCode { get; set; }

    [MaxLength(50)]
    public string? DeliveryLocationInPlantCode { get; set; }

    [MaxLength(50)]
    public string? DeliveryProcessCode { get; set; }

    [MaxLength(50)]
    public string? DeliveryPlantCode { get; set; }

    [MaxLength(20)]
    public string? ColorRelatedType1digit { get; set; }

    [MaxLength(50)]
    public string? ProcessSeqId { get; set; }

    [MaxLength(50)]
    public string? DeliveryType { get; set; }

    [MaxLength(50)]
    public string? LeadTime { get; set; }

    [MaxLength(50)]
    public string? MaterialPartNo { get; set; }

    [MaxLength(20)]
    public string? AplySDate { get; set; }

    [MaxLength(20)]
    public string? AplyEDate { get; set; }

    [MaxLength(20)]
    public string? OutputSEQ { get; set; }

    public DateTime CreatedDate { get; set; } = DateTime.Now;

    [Required]
    [MaxLength(100)]
    public string CreatedBy { get; set; } = string.Empty;

    // Navigation property
    [ForeignKey("FileUploadId")]
    public virtual FileUpload FileUpload { get; set; } = null!;
}
