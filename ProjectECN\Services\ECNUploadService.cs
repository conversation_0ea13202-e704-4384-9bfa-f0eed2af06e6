using System.Globalization;
using System.IO.Compression;
using Microsoft.AspNetCore.Components.Forms;

namespace ProjectECN.Services;

public class ECNUploadService(
    IWebHostEnvironment environment,
    ILogger<ECNUploadService> logger,
    ECNImageProcessingService imageProcessingService,
    ECNFactoryOcrService ocrService,
    PSMCEcnDataService dataService,
    ECNExcelProcessingService excelProcessingService,
    ECNDatabaseService databaseService)
{
    public async Task<UploadResult> UploadAndExtractECNFileAsync(IBrowserFile file, IProgress<UploadProgress>? progress,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation($"Starting upload process for file: {file.Name}, Size: {file.Size}");

            // Validate file name format (YYYYMMDD.zip)
            var fileName = file.Name;
            if (!IsValidECNFileName(fileName))
            {
                logger.LogWarning($"Invalid file name format: {fileName}");
                return new UploadResult
                    { Success = false, ErrorMessage = "Invalid file name format. Expected format: YYYYMMDD.zip" };
            }

            progress?.Report(new UploadProgress { Stage = "Validating", Percentage = 10 });
            cancellationToken.ThrowIfCancellationRequested();

            // Extract date from filename for re-upload logic
            var dateFromFileName = Path.GetFileNameWithoutExtension(fileName);
            var uploadDate = DateTime.ParseExact(dateFromFileName, "yyyyMMdd", null);

            // Check if file already exists
            var zipFolderPath = Path.Combine(environment.WebRootPath, "enc-zip");
            var zipFilePath = Path.Combine(zipFolderPath, fileName);
            var extractFolderPath = Path.Combine(environment.WebRootPath, "enc");
            var dateSpecificExtractPath = Path.Combine(extractFolderPath, dateFromFileName);

            logger.LogInformation($"Zip folder path: {zipFolderPath}");
            logger.LogInformation($"Zip file path: {zipFilePath}");
            logger.LogInformation($"Extract path for date {dateFromFileName}: {dateSpecificExtractPath}");

            // Handle re-upload scenario: delete existing folder and database records
            if (Directory.Exists(dateSpecificExtractPath))
            {
                logger.LogInformation($"Re-upload detected for date {dateFromFileName}. Deleting existing folder and database records.");

                progress?.Report(new UploadProgress { Stage = "Cleaning existing data", Percentage = 5 });

                // Delete existing ECN folder for this date
                try
                {
                    Directory.Delete(dateSpecificExtractPath, true);
                    logger.LogInformation($"Deleted existing ECN folder: {dateSpecificExtractPath}");
                }
                catch (Exception ex)
                {
                    logger.LogWarning($"Could not delete existing ECN folder: {ex.Message}");
                }

                // Delete existing database records for this date
                var deleteResult = await databaseService.DeleteECNDataByDateAsync(uploadDate);
                if (deleteResult.Success)
                {
                    logger.LogInformation($"Deleted existing database records: {deleteResult.Message}");
                }
                else
                {
                    logger.LogWarning($"Could not delete existing database records: {deleteResult.ErrorMessage}");
                }
            }

            if (File.Exists(zipFilePath))
            {
                logger.LogWarning($"Zip file already exists, deleting: {zipFilePath}");
                File.Delete(zipFilePath);
            }

            progress?.Report(new UploadProgress { Stage = "Uploading", Percentage = 20 });
            cancellationToken.ThrowIfCancellationRequested();

            // Ensure directories exist
            Directory.CreateDirectory(zipFolderPath);
            Directory.CreateDirectory(extractFolderPath);

            logger.LogInformation($"Created directories - Zip: {zipFolderPath}, Extract: {extractFolderPath}");

            // Upload file
            logger.LogInformation($"Starting file upload to: {zipFilePath}");
            try
            {
                await using var fileStream = new FileStream(zipFilePath, FileMode.Create);
                await using var browserFileStream = file.OpenReadStream(100 * 1024 * 1024);
                await browserFileStream.CopyToAsync(fileStream, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during file upload");
                throw new InvalidOperationException($"Failed to upload file: {ex.Message}", ex);
            }

            logger.LogInformation(
                $"File uploaded successfully. File size on disk: {new FileInfo(zipFilePath).Length}");

            progress?.Report(new UploadProgress { Stage = "Extracting", Percentage = 60 });
            cancellationToken.ThrowIfCancellationRequested();

            // Extract ZIP file
            logger.LogInformation($"Starting extraction to: {extractFolderPath}");
            await ExtractZipFileAsync(zipFilePath, extractFolderPath, progress, cancellationToken);

            progress?.Report(new UploadProgress { Stage = "Processing Images", Percentage = 70 });
            cancellationToken.ThrowIfCancellationRequested();

            // Process TIFF images to extract ECN portions
            logger.LogInformation("Starting image processing for extracted files");
            var imageProgress = new Progress<ImageProcessingProgress>(p =>
            {
                var overallProgress = 70 + (int)(p.Percentage * 0.2); // 70-90% range for image processing
                progress?.Report(new UploadProgress
                    { Stage = $"Processing Images: {p.CurrentFile}", Percentage = overallProgress });
            });

            var imageResult = await imageProcessingService.ProcessECNImagesAsync(extractFolderPath, imageProgress);
            if (!imageResult.Success)
                logger.LogWarning($"Image processing completed with errors: {imageResult.ErrorMessage}");

            progress?.Report(new UploadProgress { Stage = "Processing OCR", Percentage = 80 });
            cancellationToken.ThrowIfCancellationRequested();

            // Process factory images with OCR to detect PSMC ECNs
            logger.LogInformation("Starting OCR processing for factory images");
            var ocrProgress = new Progress<FactoryOcrProgress>(p =>
            {
                var overallProgress = 80 + (int)(p.Percentage * 0.1); // 80-90% range for OCR processing
                progress?.Report(new UploadProgress
                    { Stage = $"OCR Processing: {p.CurrentImage}", Percentage = overallProgress });
            });

            var psmcEcns = await ocrService.ProcessFactoryImagesAsync(extractFolderPath, ocrProgress);
            var qualifiedCount = psmcEcns.Count(e => e.QualifiesForPSMC);

            logger.LogInformation(
                $"OCR processing completed. Found {qualifiedCount} ECNs qualifying for PSMC out of {psmcEcns.Count} processed.");

            // Save OCR results to persistent storage
            if (psmcEcns.Any())
            {
                await dataService.SavePSMCEcnDataAsync(psmcEcns);
                logger.LogInformation($"Saved {psmcEcns.Count} PSMC ECN records to data storage");
            }

            progress?.Report(new UploadProgress { Stage = "Processing Excel Files", Percentage = 85 });
            cancellationToken.ThrowIfCancellationRequested();

            // Process Excel files and save to database
            logger.LogInformation("Starting Excel processing for ECN data");
            var excelProgress = new Progress<ExcelProcessingProgress>(p =>
            {
                var overallProgress = 85 + (int)(p.Percentage * 0.05); // 85-90% range for Excel processing
                progress?.Report(new UploadProgress
                    { Stage = $"Processing Excel: {p.CurrentFile}", Percentage = overallProgress });
            });

            // Get current user ID (you may need to inject IHttpContextAccessor or pass it as parameter)
            var currentUserId = "system"; // TODO: Get actual user ID from authentication context

            var excelResult = await excelProcessingService.ProcessECNExcelFilesAsync(extractFolderPath, psmcEcns, currentUserId, excelProgress);

            if (excelResult.Success && excelResult.EcnData.Any())
            {
                // Save Excel data to database
                var dbResult = await databaseService.SaveECNDataAsync(excelResult.EcnData);
                if (dbResult.Success)
                {
                    logger.LogInformation($"Successfully saved {dbResult.RecordsAffected} ECN records to database");
                }
                else
                {
                    logger.LogError($"Failed to save ECN data to database: {dbResult.ErrorMessage}");
                }
            }
            else if (!excelResult.Success)
            {
                logger.LogWarning($"Excel processing failed: {excelResult.ErrorMessage}");
            }
            else
            {
                logger.LogInformation("No Excel data found to save to database");
            }

            progress?.Report(new UploadProgress { Stage = "Cleaning up", Percentage = 90 });
            cancellationToken.ThrowIfCancellationRequested();

            // Delete the uploaded ZIP file
            logger.LogInformation($"Deleting ZIP file: {zipFilePath}");
            File.Delete(zipFilePath);

            progress?.Report(new UploadProgress { Stage = "Complete", Percentage = 100 });

            var successMessage = $"Successfully uploaded and extracted {fileName}";
            if (imageResult.Success)
                successMessage += $". Processed {imageResult.ProcessedFiles} TIFF images.";
            else
                successMessage += $". Image processing had some errors: {imageResult.ErrorMessage}";

            // Add OCR results to success message
            if (psmcEcns.Any())
            {
                var ocrQualifiedCount = psmcEcns.Count(e => e.QualifiesForPSMC);
                successMessage +=
                    $" OCR processed {psmcEcns.Count} factory images, {ocrQualifiedCount} ECNs qualify for PSMC.";
            }

            // Add Excel processing results to success message
            if (excelResult.Success)
            {
                successMessage += $" Processed {excelResult.ProcessedFiles} Excel files with {excelResult.EcnData.Count} total records saved to database.";
            }
            else if (!string.IsNullOrEmpty(excelResult.ErrorMessage))
            {
                successMessage += $" Excel processing had errors: {excelResult.ErrorMessage}";
            }

            logger.LogInformation($"Successfully processed ECN file: {fileName}");
            return new UploadResult { Success = true, Message = successMessage };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error processing ECN file: {file.Name}");
            return new UploadResult { Success = false, ErrorMessage = $"Error processing file: {ex.Message}" };
        }
    }

    private bool IsValidECNFileName(string fileName)
    {
        if (string.IsNullOrEmpty(fileName) || !fileName.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
            return false;

        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

        // Check if it's exactly 8 digits (YYYYMMDD)
        if (nameWithoutExtension.Length != 8)
            return false;

        // Check if all characters are digits
        if (!nameWithoutExtension.All(char.IsDigit))
            return false;

        // Validate date format
        if (DateTime.TryParseExact(nameWithoutExtension, "yyyyMMdd", null, DateTimeStyles.None, out _))
            return true;

        return false;
    }

    private async Task ExtractZipFileAsync(string zipFilePath, string extractPath, IProgress<UploadProgress>? progress,
        CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            using var archive = ZipFile.OpenRead(zipFilePath);
            var totalEntries = archive.Entries.Count;
            var processedEntries = 0;

            foreach (var entry in archive.Entries)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Skip directories
                if (string.IsNullOrEmpty(entry.Name))
                    continue;

                var destinationPath = Path.Combine(extractPath, entry.FullName);

                // Ensure the directory exists
                var directoryPath = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(directoryPath)) Directory.CreateDirectory(directoryPath);

                // Extract the file
                entry.ExtractToFile(destinationPath, true);

                processedEntries++;
                var extractProgress = 60 + (int)((double)processedEntries / totalEntries * 30);
                progress?.Report(new UploadProgress { Stage = "Extracting", Percentage = extractProgress });
            }
        }, cancellationToken);
    }
}

public class UploadResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
}

public class UploadProgress
{
    public string Stage { get; set; } = string.Empty;
    public int Percentage { get; set; }
}