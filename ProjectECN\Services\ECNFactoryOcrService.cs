using System.Drawing;
using System.Globalization;
using ProjectECN.DTO;
using Tesseract;

namespace ProjectECN.Services;

public class ECNFactoryOcrService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<ECNFactoryOcrService> _logger;
    private readonly string _tessDataPath;

    public ECNFactoryOcrService(ILogger<ECNFactoryOcrService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
        _tessDataPath = Path.Combine(_environment.ContentRootPath, "tessdata");

        // Ensure tessdata directory exists
        if (!Directory.Exists(_tessDataPath))
        {
            Directory.CreateDirectory(_tessDataPath);
            _logger.LogWarning(
                $"Tessdata directory created at: {_tessDataPath}. Please ensure eng.traineddata is present.");
        }
    }

    public async Task<List<PSMCEcnDto>> ProcessFactoryImagesAsync(string extractedFolderPath,
        IProgress<FactoryOcrProgress>? progress = null)
    {
        var results = new List<PSMCEcnDto>();
        var processedCount = 0;

        try
        {
            _logger.LogInformation($"Starting factory OCR processing for folder: {extractedFolderPath}");

            // Find all factory PNG images
            var factoryImages = FindFactoryImages(extractedFolderPath);
            var totalImages = factoryImages.Count;

            _logger.LogInformation($"Found {totalImages} factory images to process");
            progress?.Report(new FactoryOcrProgress
                { Stage = "Scanning", ProcessedImages = 0, TotalImages = totalImages });

            foreach (var factoryImagePath in factoryImages)
                try
                {
                    _logger.LogInformation($"Processing factory image: {factoryImagePath}");
                    var ecnDto = await ProcessSingleFactoryImage(factoryImagePath);

                    if (ecnDto != null)
                    {
                        results.Add(ecnDto);

                        if (ecnDto.QualifiesForPSMC)
                            _logger.LogInformation($"ECN {ecnDto.EcnNumber} qualifies for PSMC processing");
                    }

                    processedCount++;
                    progress?.Report(new FactoryOcrProgress
                    {
                        Stage = "Processing OCR",
                        ProcessedImages = processedCount,
                        TotalImages = totalImages,
                        CurrentImage = Path.GetFileName(factoryImagePath),
                        PSMCQualified = results.Count(r => r.QualifiesForPSMC)
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing factory image: {factoryImagePath}");
                }

            var qualifiedCount = results.Count(r => r.QualifiesForPSMC);
            _logger.LogInformation(
                $"Factory OCR processing completed. {qualifiedCount} ECNs qualify for PSMC out of {processedCount} processed.");

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during factory OCR processing");
            return results;
        }
    }

    private List<string> FindFactoryImages(string rootPath)
    {
        var factoryImages = new List<string>();

        if (!Directory.Exists(rootPath))
        {
            _logger.LogWarning($"Root path does not exist: {rootPath}");
            return factoryImages;
        }

        // Look for date folders (YYYYMMDD format)
        var dateFolders = Directory.GetDirectories(rootPath)
            .Where(dir => IsValidDateFolder(Path.GetFileName(dir)))
            .ToList();

        foreach (var dateFolder in dateFolders)
        {
            // Look for 2W and 4W folders
            var wheelFolders = Directory.GetDirectories(dateFolder)
                .Where(dir => Path.GetFileName(dir).Equals("2W", StringComparison.OrdinalIgnoreCase) ||
                              Path.GetFileName(dir).Equals("4W", StringComparison.OrdinalIgnoreCase))
                .ToList();

            foreach (var wheelFolder in wheelFolders)
            {
                // Find all factory PNG files
                var factoryPngs = Directory.GetFiles(wheelFolder, "*_factory.png", SearchOption.AllDirectories);
                factoryImages.AddRange(factoryPngs);
                _logger.LogInformation($"Found {factoryPngs.Length} factory images in {wheelFolder}");
            }
        }

        return factoryImages;
    }

    private bool IsValidDateFolder(string folderName)
    {
        return folderName.Length == 8 &&
               folderName.All(char.IsDigit) &&
               DateTime.TryParseExact(folderName, "yyyyMMdd", null, DateTimeStyles.None, out _);
    }

    private async Task<PSMCEcnDto?> ProcessSingleFactoryImage(string factoryImagePath)
    {
        return await Task.Run(() =>
        {
            try
            {
                // Extract ECN information from file path
                var ecnInfo = ExtractEcnInfoFromPath(factoryImagePath);
                if (ecnInfo == null) return null;

                var ecnDto = new PSMCEcnDto
                {
                    EcnNumber = ecnInfo.Value.EcnNumber,
                    FileName = ecnInfo.Value.FileName,
                    ProcessDate = ecnInfo.Value.ProcessDate,
                    VehicleType = ecnInfo.Value.VehicleType,
                    FactoryImagePath = factoryImagePath,
                    DescriptionImagePath = factoryImagePath.Replace("_factory.png", "_description.png"),
                    OriginalFilePath = factoryImagePath.Replace("_factory.png", ".tif")
                };

                // Perform OCR on the factory image
                var ocrResult = PerformOCR(factoryImagePath);
                ecnDto.FactoryOcrText = ocrResult.Text;
                ecnDto.OcrConfidence = ocrResult.Confidence;

                // Check for PSMC text
                ecnDto.HasPSMCText = CheckForPSMCText(ocrResult.Text);
                if (ecnDto.HasPSMCText) ecnDto.DetectedFactories.Add("PSMC");

                // Detect checkboxes and check if OTHERS is marked
                ecnDto.CheckboxResults = DetectCheckboxes(factoryImagePath);
                ecnDto.IsOthersCheckboxMarked = ecnDto.CheckboxResults.Any(cb =>
                    cb.Label.Contains("OTHERS", StringComparison.OrdinalIgnoreCase) && cb.IsChecked);

                // Add processing notes
                if (ecnDto.QualifiesForPSMC)
                {
                    ecnDto.ProcessingNotes.Add("Qualifies for PSMC: OTHERS checkbox marked and PSMC text detected");
                }
                else
                {
                    if (!ecnDto.IsOthersCheckboxMarked)
                        ecnDto.ProcessingNotes.Add("OTHERS checkbox not marked");
                    if (!ecnDto.HasPSMCText)
                        ecnDto.ProcessingNotes.Add("PSMC text not detected");
                }

                return ecnDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing factory image: {factoryImagePath}");
                return null;
            }
        });
    }

    private (string EcnNumber, string FileName, string ProcessDate, string VehicleType)? ExtractEcnInfoFromPath(
        string filePath)
    {
        try
        {
            // Example path: wwwroot/enc/20250702/4W/Other/ECN/52U-0270_124510_factory.png
            var pathParts = filePath.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);

            var fileName = Path.GetFileNameWithoutExtension(filePath).Replace("_factory", "");
            var processDate = pathParts.FirstOrDefault(p => IsValidDateFolder(p)) ?? "";
            var vehicleType = pathParts.FirstOrDefault(p => p.Equals("2W") || p.Equals("4W")) ?? "";

            // Extract ECN number (assuming format like 52U-0270_124510)
            var ecnNumber = "";
            var fileNameParts = fileName.Split('_');
            if (fileNameParts.Length > 0) ecnNumber = fileNameParts[0]; // 52U-0270

            return (ecnNumber, fileName, processDate, vehicleType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error extracting ECN info from path: {filePath}");
            return null;
        }
    }

    private (string Text, float Confidence) PerformOCR(string imagePath)
    {
        try
        {
            using (var engine = new TesseractEngine(_tessDataPath, "eng", EngineMode.Default))
            {
                using (var img = Pix.LoadFromFile(imagePath))
                {
                    using (var page = engine.Process(img))
                    {
                        var text = page.GetText();
                        var confidence = page.GetMeanConfidence();

                        _logger.LogInformation(
                            $"OCR completed for {Path.GetFileName(imagePath)}. Confidence: {confidence:F2}");
                        return (text, confidence * 100);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"OCR failed for image: {imagePath}");
            return ("", 0);
        }
    }

    private bool CheckForPSMCText(string ocrText)
    {
        if (string.IsNullOrWhiteSpace(ocrText)) return false;

        var psmc_patterns = new[]
        {
            "PSMC",
            "P.S.M.C",
            "P S M C",
            "PSMC.",
            "PSMC,"
        };

        return psmc_patterns.Any(pattern =>
            ocrText.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private List<CheckboxDetectionResult> DetectCheckboxes(string imagePath)
    {
        var results = new List<CheckboxDetectionResult>();

        try
        {
            using (var bitmap = new Bitmap(imagePath))
            {
                // Simple checkbox detection based on dark pixel density
                // This is a basic implementation - can be enhanced with more sophisticated algorithms

                // Look for potential checkbox areas (small square regions with high contrast)
                var checkboxSize = 20; // Approximate checkbox size in pixels
                var threshold = 0.3f; // Threshold for considering a checkbox "checked"

                for (var y = 0; y < bitmap.Height - checkboxSize; y += 5)
                for (var x = 0; x < bitmap.Width - checkboxSize; x += 5)
                {
                    var darkPixelRatio = CalculateDarkPixelRatio(bitmap, x, y, checkboxSize, checkboxSize);

                    if (darkPixelRatio > threshold)
                        // Potential checkbox found
                        results.Add(new CheckboxDetectionResult
                        {
                            X = x,
                            Y = y,
                            Width = checkboxSize,
                            Height = checkboxSize,
                            IsChecked = darkPixelRatio > 0.5f,
                            Confidence = darkPixelRatio * 100,
                            DetectionMethod = "PixelDensity",
                            Label =
                                "OTHERS" // Simplified - in real implementation, would need to associate with nearby text
                        });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error detecting checkboxes in image: {imagePath}");
        }

        return results;
    }

    private float CalculateDarkPixelRatio(Bitmap bitmap, int startX, int startY, int width, int height)
    {
        var darkPixels = 0;
        var totalPixels = width * height;

        for (var y = startY; y < startY + height && y < bitmap.Height; y++)
        for (var x = startX; x < startX + width && x < bitmap.Width; x++)
        {
            var pixel = bitmap.GetPixel(x, y);
            var brightness = (pixel.R + pixel.G + pixel.B) / 3;

            if (brightness < 128) // Consider pixels darker than middle gray as "dark"
                darkPixels++;
        }

        return (float)darkPixels / totalPixels;
    }
}

public class FactoryOcrProgress
{
    public string Stage { get; set; } = string.Empty;
    public int ProcessedImages { get; set; }
    public int TotalImages { get; set; }
    public string CurrentImage { get; set; } = string.Empty;
    public int PSMCQualified { get; set; }
    public int Percentage => TotalImages > 0 ? ProcessedImages * 100 / TotalImages : 0;
}