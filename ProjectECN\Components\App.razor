﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="@Assets["app.css"]" />
    <link rel="stylesheet" href="@Assets["ProjectECN.styles.css"]" />
    <ImportMap />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <HeadOutlet />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />

</head>

<body>
    <Routes />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script>
        window.downloadFile = function (filename, contentType, content) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        };

        window.clickFileInput = function (elementId) {
            try {
                const fileInput = document.getElementById(elementId);
                if (fileInput) {
                    fileInput.click();
                    return true;
                }
                return false;
            } catch (e) {
                console.error("Error clicking file input:", e);
                return false;
            }
        };
    </script>

</body>

</html>
