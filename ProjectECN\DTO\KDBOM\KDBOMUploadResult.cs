namespace ProjectECN.DTO.KDBOM;

public class KDBOMUploadResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public int ProcessedFiles { get; set; }
    public int TotalRecords { get; set; }
    public int ModelInfoRecords { get; set; }
    public int PartsDataRecords { get; set; }
    public List<string> ProcessedSheets { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}

public class KDBOMUploadProgress
{
    public string Stage { get; set; } = string.Empty;
    public int Percentage { get; set; }
    public string CurrentFile { get; set; } = string.Empty;
    public string CurrentSheet { get; set; } = string.Empty;
    public int ProcessedRecords { get; set; }
    public int TotalRecords { get; set; }
}

public class KDBOMFileInfo
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string UploadedBy { get; set; } = string.Empty;
    public DateTime UploadedDate { get; set; }
    public DateTime? ProcessedDate { get; set; }
    public string ProcessingStatus { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public int? RecordCount { get; set; }
    public string FormattedFileSize => FormatFileSize(FileSize);
    public string FormattedUploadDate => UploadedDate.ToString("yyyy-MM-dd HH:mm:ss");
    public string FormattedProcessedDate => ProcessedDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Not processed";

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

public class KDBOMPartsDataDto
{
    public Guid Id { get; set; }
    public string SheetName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string UploadedBy { get; set; } = string.Empty;
    public DateTime UploadedDate { get; set; }
    public string KDModelCode { get; set; } = string.Empty;
    public string PartNo { get; set; } = string.Empty;
    public string InstallLocationComment { get; set; } = string.Empty;
    public string QTY { get; set; } = string.Empty;
    public string BOMGName { get; set; } = string.Empty;
    public string Dealer { get; set; } = string.Empty;
    public string CCCode { get; set; } = string.Empty;
    public string FormattedUploadDate => UploadedDate.ToString("yyyy-MM-dd");
}
