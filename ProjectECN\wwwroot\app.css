@import '/_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css';

body {
    --body-font: "Segoe UI Variable", "Segoe UI", sans-serif;
    font-family: var(--body-font);
    font-size: var(--type-ramp-base-font-size);
    line-height: var(--type-ramp-base-line-height);
    margin: 0;
}

.navmenu-icon {
    display: none;
}

.main {
    min-height: calc(100dvh - 86px);
    color: var(--neutral-foreground-rest);
    align-items: stretch !important;
}

.body-content {
    align-self: stretch;
    height: calc(100dvh - 86px) !important;
    display: flex;
}

.content {
    padding: 0.5rem 1.5rem;
    align-self: stretch !important;
    width: 100%;
}

.manage {
    width: 100dvw;
}

footer {
    background: var(--neutral-layer-4);
    color: var(--neutral-foreground-rest);
    align-items: center;
    padding: 10px 10px;
}

    footer a {
        color: var(--neutral-foreground-rest);
        text-decoration: none;
    }

        footer a:focus {
            outline: 1px dashed;
            outline-offset: 3px;
        }

        footer a:hover {
            text-decoration: underline;
        }

.alert {
    border: 1px dashed var(--accent-fill-rest);
    padding: 5px;
}


#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    margin: 20px 0;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::before {
        content: "An error has occurred. "
    }

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

@media (max-width: 600px) {
    .header-gutters {
        margin: 0.5rem 3rem 0.5rem 1.5rem !important;
    }

    [dir="rtl"] .header-gutters {
        margin: 0.5rem 1.5rem 0.5rem 3rem !important;
    }

    .main {
        flex-direction: column !important;
        row-gap: 0 !important;
    }

    nav.sitenav {
        width: 100%;
        height: 100%;
    }

    #main-menu {
        width: 100% !important;
    }

        #main-menu > div:first-child:is(.expander) {
            display: none;
        }

    .navmenu {
        width: 100%;
    }

    #navmenu-toggle {
        appearance: none;
    }

        #navmenu-toggle ~ nav {
            display: none;
        }

        #navmenu-toggle:checked ~ nav {
            display: block;
        }

    .navmenu-icon {
        cursor: pointer;
        z-index: 10;
        display: block;
        position: absolute;
        top: 15px;
        left: unset;
        right: 20px;
        width: 20px;
        height: 20px;
        border: none;
    }

    [dir="rtl"] .navmenu-icon {
        left: 20px;
        right: unset;
    }
}

/* ECN Upload Page Styles */
.ecn-upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.upload-header {
    text-align: center;
    margin-bottom: 2rem;
}

.upload-header h2 {
    margin: 0.5rem 0;
    color: var(--neutral-foreground-rest);
    font-size: 2rem;
    font-weight: 600;
}

.upload-description {
    color: var(--neutral-foreground-hint);
    font-size: 1.1rem;
    margin: 0;
}

.upload-card {
    background: var(--neutral-layer-1);
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.file-drop-zone {
    border: 2px dashed var(--neutral-stroke-rest);
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: var(--neutral-layer-2);
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
    border-color: var(--accent-fill-rest);
    background: var(--accent-fill-subtle);
    transform: translateY(-2px);
}

.file-drop-zone h3 {
    margin: 1rem 0 0.5rem 0;
    color: var(--neutral-foreground-rest);
    font-size: 1.5rem;
}

.file-drop-zone p {
    color: var(--neutral-foreground-hint);
    margin: 0.5rem 0;
}

.file-format-info {
    font-size: 0.9rem;
    color: var(--neutral-foreground-hint);
    font-style: italic;
    margin-bottom: 1.5rem !important;
}

.hidden-file-input {
    display: none;
}

.selected-file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--accent-fill-subtle);
    border: 1px solid var(--accent-stroke-rest);
    border-radius: 8px;
    margin-top: 1rem;
}

.file-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-size {
    font-size: 0.9rem;
    color: var(--neutral-foreground-hint);
}

.upload-progress-container {
    text-align: center;
    padding: 2rem;
}

.upload-actions {
    margin-top: 1.5rem;
    text-align: center;
}

.progress-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.progress-header h3 {
    margin: 0;
    color: var(--neutral-foreground-rest);
}

.progress-details {
    margin-bottom: 1.5rem;
}

.current-stage {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--neutral-foreground-rest);
}

.upload-progress-bar {
    width: 100%;
    height: 8px;
    margin: 1rem 0;
}

.progress-percentage {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-fill-rest);
    margin-top: 0.5rem;
}

.processing-file {
    color: var(--neutral-foreground-hint);
    font-size: 0.9rem;
    margin-top: 1rem;
}

.result-message {
    margin-top: 1rem;
}

.upload-instructions {
    background: var(--neutral-layer-2);
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid var(--accent-fill-rest);
}

.upload-instructions h4 {
    margin: 0 0 1rem 0;
    color: var(--neutral-foreground-rest);
    font-size: 1.1rem;
}

.upload-instructions ul {
    margin: 0;
    padding-left: 1.5rem;
}

.upload-instructions li {
    margin-bottom: 0.5rem;
    color: var(--neutral-foreground-rest);
    line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .ecn-upload-container {
        padding: 1rem;
    }

    .upload-card {
        padding: 1.5rem;
    }

    .file-drop-zone {
        padding: 2rem 1rem;
    }

    .selected-file-info {
        flex-direction: column;
        text-align: center;
    }
}

/* Native HTML Controls Styles */
.file-input-section {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #f9f9f9;
    margin-bottom: 1rem;
}

.file-input-section h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.5rem;
}

.file-input {
    display: none;
}

.browse-button {
    background: #0078d4;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s;
    margin-top: 1rem;
}

.browse-button:hover {
    background: #106ebe;
}

.browse-button:active {
    background: #005a9e;
}

.upload-button {
    background: #107c10;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.upload-button:hover {
    background: #0e6e0e;
}

.cancel-button {
    background: #d13438;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.cancel-button:hover {
    background: #b52d32;
}

.file-icon {
    font-size: 24px;
    margin-right: 10px;
}

.progress-bar-container {
    margin: 1rem 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background-color: #0078d4;
    transition: width 0.3s ease;
}

.message-bar {
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
    font-weight: 500;
}

.message-bar.success {
    background-color: #dff6dd;
    color: #0e5814;
    border: 1px solid #0e5814;
}

.message-bar.error {
    background-color: #fde7e9;
    color: #a4262c;
    border: 1px solid #a4262c;
}

/* PSMC ECN List Styles */
.psmc-ecn-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h2 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 2rem;
    font-weight: 600;
}

.page-description {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-icon {
    font-size: 2rem;
}

.card-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #0078d4;
}

.card-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.controls-section {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.search-controls {
    display: flex;
    gap: 0.5rem;
    flex: 1;
    min-width: 250px;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.search-button {
    background: #0078d4;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-select, .date-input {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.action-controls {
    display: flex;
    gap: 0.5rem;
}

.refresh-button, .export-button {
    background: #107c10;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.export-button {
    background: #8764b8;
}

.loading-container {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.ecn-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.ecn-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.ecn-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.ecn-card.qualified {
    border-left: 4px solid #107c10;
}

.ecn-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.ecn-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.vehicle-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
}

.vehicle-badge.2w {
    background: #0078d4;
}

.vehicle-badge.4w {
    background: #107c10;
}

.ecn-details {
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-row .label {
    font-weight: 500;
    color: #666;
}

.detail-row .value {
    color: #333;
}

.status-indicators {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    background: #f5f5f5;
    font-size: 0.9rem;
}

.status-item.active {
    background: #dff6dd;
    color: #0e5814;
}

.processing-notes {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.processing-notes ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.ecn-actions {
    display: flex;
    gap: 0.5rem;
}

.view-images-button, .view-ocr-button {
    flex: 1;
    background: #0078d4;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.view-ocr-button {
    background: #8764b8;
}

.no-data {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-data-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.ocr-text pre {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.ocr-details p {
    margin: 0.5rem 0;
    color: #666;
}
