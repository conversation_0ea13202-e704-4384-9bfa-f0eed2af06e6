using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectECN.Models.KDBOM;

[Table("FileUploads", Schema = "kdbom")]
public class FileUpload
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [MaxLength(255)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    public string OriginalFileName { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string FilePath { get; set; } = string.Empty;

    public long FileSize { get; set; }

    [Required]
    [MaxLength(100)]
    public string UploadedBy { get; set; } = string.Empty;

    public DateTime UploadedDate { get; set; } = DateTime.Now;

    public DateTime? ProcessedDate { get; set; }

    [Required]
    [MaxLength(50)]
    public string ProcessingStatus { get; set; } = "Pending"; // Pending, Processing, Completed, Failed

    public string? ErrorMessage { get; set; }

    public int? RecordCount { get; set; }

    public DateTime CreatedDate { get; set; } = DateTime.Now;

    [Required]
    [MaxLength(100)]
    public string CreatedBy { get; set; } = string.Empty;

    public DateTime? ModifiedDate { get; set; }

    [MaxLength(100)]
    public string? ModifiedBy { get; set; }

    // Navigation properties
    public virtual ICollection<ModelInfo> ModelInfos { get; set; } = new List<ModelInfo>();
    public virtual ICollection<PartsData> PartsData { get; set; } = new List<PartsData>();
}
