@page "/kdbom/import"
@rendermode InteractiveServer
@inject KDBOMService KDBOMService
@inject IJSRuntime JSRuntime
@using ProjectECN.Services
@using ProjectECN.DTO.KDBOM
@using Microsoft.AspNetCore.Components.Forms
@implements IAsyncDisposable

<div class="kdbom-import-container">
    <div class="page-header">
        <h2>📊 KDBOM Import</h2>
        <p class="page-description">Import KDBOM Excel files with multiple sheets. System will clear existing data and import all selected files.</p>
    </div>

    <div class="import-card">
        @if (!_isProcessing)
        {
            <div class="file-selection-section">
                <h3>Select KDBOM Excel Files</h3>
                <p class="file-format-info">Select all KDBOM Excel files (.xlsx, .xls) to import at once</p>

                <div class="file-input-wrapper">
                    <InputFile @ref="_fileInput"
                               accept=".xlsx,.xls"
                               OnChange="HandleFilesSelected"
                               class="file-input"
                               multiple="true"/>
                    
                    <button type="button"
                            class="browse-button"
                            @onclick="OpenFileDialog">
                        📁 Browse Files
                    </button>
                </div>

                @if (_selectedFiles.Any())
                {
                    <div class="selected-files-section">
                        <h4>Selected Files (@_selectedFiles.Count)</h4>
                        <div class="files-list">
                            @foreach (var file in _selectedFiles)
                            {
                                <div class="file-item">
                                    <div class="file-icon">📄</div>
                                    <div class="file-details">
                                        <strong>@file.Name</strong>
                                        <span class="file-size">@FormatFileSize(file.Size)</span>
                                    </div>
                                    <button type="button"
                                            class="remove-file-btn"
                                            @onclick="() => RemoveFile(file)">
                                        ❌
                                    </button>
                                </div>
                            }
                        </div>
                        
                        <div class="import-actions">
                            <button type="button"
                                    class="clear-button"
                                    @onclick="ClearSelectedFiles">
                                🗑️ Clear All
                            </button>
                            <button type="button"
                                    class="import-button"
                                    @onclick="StartImport">
                                ⬆️ Import Files
                            </button>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="processing-section">
                <div class="progress-container">
                    <h3>Processing KDBOM Files...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: @(_progress.Percentage)%"></div>
                    </div>
                    <div class="progress-info">
                        <span class="progress-text">@_progress.Stage</span>
                        <span class="progress-percentage">@_progress.Percentage%</span>
                    </div>
                    @if (!string.IsNullOrEmpty(_progress.CurrentFile))
                    {
                        <div class="current-file">
                            <strong>Current File:</strong> @_progress.CurrentFile
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(_progress.CurrentSheet))
                    {
                        <div class="current-sheet">
                            <strong>Current Sheet:</strong> @_progress.CurrentSheet
                        </div>
                    }
                </div>
            </div>
        }

        @if (_importResult != null)
        {
            <div class="result-section">
                @if (_importResult.Success)
                {
                    <div class="success-message">
                        <h3>✅ Import Successful</h3>
                        <div class="result-details">
                            <div class="result-item">
                                <span class="label">Files Processed:</span>
                                <span class="value">@_importResult.ProcessedFiles</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Total Records:</span>
                                <span class="value">@_importResult.TotalRecords.ToString("N0")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Model Info Records:</span>
                                <span class="value">@_importResult.ModelInfoRecords</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Parts Data Records:</span>
                                <span class="value">@_importResult.PartsDataRecords.ToString("N0")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Processing Time:</span>
                                <span class="value">@_importResult.ProcessingTime.ToString(@"mm\:ss")</span>
                            </div>
                        </div>
                        
                        @if (_importResult.ProcessedSheets.Any())
                        {
                            <div class="processed-sheets">
                                <h4>Processed Sheets:</h4>
                                <ul>
                                    @foreach (var sheet in _importResult.ProcessedSheets)
                                    {
                                        <li>@sheet</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="error-message">
                        <h3>❌ Import Failed</h3>
                        <p>@_importResult.ErrorMessage</p>
                        
                        @if (_importResult.Errors.Any())
                        {
                            <div class="error-details">
                                <h4>Errors:</h4>
                                <ul>
                                    @foreach (var error in _importResult.Errors)
                                    {
                                        <li>@error</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                }
                
                <div class="result-actions">
                    <button type="button"
                            class="view-data-button"
                            @onclick="NavigateToDataView">
                        👁️ View Imported Data
                    </button>
                    <button type="button"
                            class="new-import-button"
                            @onclick="ResetImport">
                        🔄 New Import
                    </button>
                </div>
            </div>
        }
    </div>
</div>

@code {
    private InputFile? _fileInput;
    private List<IBrowserFile> _selectedFiles = new();
    private bool _isProcessing = false;
    private KDBOMUploadProgress _progress = new();
    private KDBOMUploadResult? _importResult;
    private CancellationTokenSource? _cancellationTokenSource;

    private async Task OpenFileDialog()
    {
        if (_fileInput?.Element != null)
        {
            await JSRuntime.InvokeVoidAsync("eval", $"document.getElementById('{_fileInput.Element.Value.Id}').click()");
        }
    }

    private async Task HandleFilesSelected(InputFileChangeEventArgs e)
    {
        _selectedFiles.Clear();
        _selectedFiles.AddRange(e.GetMultipleFiles(20)); // Allow up to 20 files
        StateHasChanged();
    }

    private void RemoveFile(IBrowserFile file)
    {
        _selectedFiles.Remove(file);
        StateHasChanged();
    }

    private void ClearSelectedFiles()
    {
        _selectedFiles.Clear();
        StateHasChanged();
    }

    private async Task StartImport()
    {
        if (!_selectedFiles.Any()) return;

        _isProcessing = true;
        _importResult = null;
        _cancellationTokenSource = new CancellationTokenSource();
        StateHasChanged();

        try
        {
            var progress = new Progress<KDBOMUploadProgress>(p =>
            {
                _progress = p;
                InvokeAsync(StateHasChanged);
            });

            // Get current user (you may need to inject IHttpContextAccessor)
            var currentUser = "system"; // TODO: Get actual user from authentication

            _importResult = await KDBOMService.ProcessKDBOMFilesAsync(_selectedFiles, currentUser, progress);
        }
        catch (Exception ex)
        {
            _importResult = new KDBOMUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
        finally
        {
            _isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task NavigateToDataView()
    {
        await JSRuntime.InvokeVoidAsync("open", "/kdbom/data", "_blank");
    }

    private void ResetImport()
    {
        _selectedFiles.Clear();
        _importResult = null;
        _progress = new KDBOMUploadProgress();
        StateHasChanged();
    }

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    public async ValueTask DisposeAsync()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
    }
}

<style>
    .kdbom-import-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .page-header h2 {
        color: var(--accent-fill-rest);
        margin: 0 0 0.5rem 0;
        font-size: 1.8rem;
    }

    .page-description {
        color: var(--neutral-foreground-rest);
        margin: 0;
        font-size: 0.95rem;
    }

    .import-card {
        background: var(--neutral-layer-1);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .file-selection-section h3 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.2rem;
    }

    .file-format-info {
        color: var(--neutral-foreground-hint);
        margin: 0 0 1rem 0;
        font-size: 0.9rem;
    }

    .file-input-wrapper {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .file-input {
        display: none;
    }

    .browse-button {
        background: var(--accent-fill-rest);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
    }

    .browse-button:hover {
        background: var(--accent-fill-hover);
    }

    .selected-files-section {
        border-top: 1px solid var(--neutral-stroke-rest);
        padding-top: 1rem;
    }

    .selected-files-section h4 {
        margin: 0 0 1rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.1rem;
    }

    .files-list {
        margin-bottom: 1rem;
    }

    .file-item {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        padding: 0.6rem;
        background: var(--neutral-layer-2);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }

    .file-icon {
        font-size: 1.2rem;
    }

    .file-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.2rem;
    }

    .file-details strong {
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
    }

    .file-size {
        color: var(--neutral-foreground-hint);
        font-size: 0.8rem;
    }

    .remove-file-btn {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.8rem;
        padding: 0.2rem;
        border-radius: 2px;
        transition: background-color 0.2s;
    }

    .remove-file-btn:hover {
        background: var(--neutral-fill-hover);
    }

    .import-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .clear-button {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
        border: 1px solid var(--neutral-stroke-rest);
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
    }

    .clear-button:hover {
        background: var(--neutral-fill-hover);
    }

    .import-button {
        background: var(--accent-fill-rest);
        color: white;
        border: none;
        padding: 0.6rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .import-button:hover {
        background: var(--accent-fill-hover);
    }

    .processing-section {
        text-align: center;
        padding: 2rem 1rem;
    }

    .progress-container h3 {
        margin: 0 0 1.5rem 0;
        color: var(--neutral-foreground-rest);
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--neutral-fill-rest);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .progress-fill {
        height: 100%;
        background: var(--accent-fill-rest);
        transition: width 0.3s ease;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .progress-text {
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
    }

    .progress-percentage {
        color: var(--accent-fill-rest);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .current-file, .current-sheet {
        color: var(--neutral-foreground-hint);
        font-size: 0.85rem;
        margin-bottom: 0.3rem;
    }

    .result-section {
        border-top: 1px solid var(--neutral-stroke-rest);
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }

    .success-message {
        background: var(--fill-color-success);
        border: 1px solid var(--stroke-color-success);
        border-radius: 6px;
        padding: 1.2rem;
        margin-bottom: 1rem;
    }

    .success-message h3 {
        margin: 0 0 1rem 0;
        color: var(--text-color-success);
        font-size: 1.2rem;
    }

    .error-message {
        background: var(--fill-color-critical);
        border: 1px solid var(--stroke-color-critical);
        border-radius: 6px;
        padding: 1.2rem;
        margin-bottom: 1rem;
    }

    .error-message h3 {
        margin: 0 0 1rem 0;
        color: var(--text-color-critical);
        font-size: 1.2rem;
    }

    .result-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.8rem;
        margin-bottom: 1rem;
    }

    .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.4rem 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .result-item .label {
        font-weight: 500;
        color: var(--neutral-foreground-rest);
    }

    .result-item .value {
        font-weight: 600;
        color: var(--accent-fill-rest);
    }

    .processed-sheets {
        margin-top: 1rem;
    }

    .processed-sheets h4 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1rem;
    }

    .processed-sheets ul {
        margin: 0;
        padding-left: 1.2rem;
        color: var(--neutral-foreground-hint);
        font-size: 0.9rem;
    }

    .processed-sheets li {
        margin-bottom: 0.2rem;
    }

    .error-details {
        margin-top: 1rem;
    }

    .error-details h4 {
        margin: 0 0 0.5rem 0;
        color: var(--text-color-critical);
        font-size: 1rem;
    }

    .error-details ul {
        margin: 0;
        padding-left: 1.2rem;
        color: var(--text-color-critical);
        font-size: 0.9rem;
    }

    .error-details li {
        margin-bottom: 0.3rem;
    }

    .result-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 1.5rem;
    }

    .view-data-button, .new-import-button {
        padding: 0.7rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: background-color 0.2s;
        text-decoration: none;
        border: none;
    }

    .view-data-button {
        background: var(--accent-fill-rest);
        color: white;
    }

    .view-data-button:hover {
        background: var(--accent-fill-hover);
    }

    .new-import-button {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
        border: 1px solid var(--neutral-stroke-rest);
    }

    .new-import-button:hover {
        background: var(--neutral-fill-hover);
    }

    @@media (max-width: 768px) {
        .kdbom-import-container {
            padding: 0.5rem;
        }

        .import-card {
            padding: 1rem;
        }

        .file-input-wrapper {
            flex-direction: column;
            align-items: stretch;
        }

        .import-actions {
            flex-direction: column;
        }

        .result-details {
            grid-template-columns: 1fr;
        }

        .result-actions {
            flex-direction: column;
        }
    }
</style>
