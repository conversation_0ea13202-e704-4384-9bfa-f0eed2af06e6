using Microsoft.AspNetCore.Authentication.Negotiate;
using OfficeOpenXml;
using Microsoft.EntityFrameworkCore;
using Microsoft.FluentUI.AspNetCore.Components;
using ProjectECN.Components;
using ProjectECN.Models;
using ProjectECN.Services;
using Syncfusion.Blazor;


var builder = WebApplication.CreateBuilder(args);

// EPPlus license is configured in appsettings.json

builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
});

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzkyODU4OEAzMzMwMmUzMDJlMzAzYjMzMzAzYmR0WHZFQ0lORFR6cUh2WURHK1VxbWVJOU4wL3V3eXU2K1RBU01TQ05NS3c9");


builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();
builder.Services.AddAuthorization(options => { options.FallbackPolicy = options.DefaultPolicy; });


// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();
builder.Services.AddFluentUIComponents();
builder.Services.AddSyncfusionBlazor();
//builder.Services.AddControllers();

// Add custom services
builder.Services.AddScoped<ECNUploadService>();
builder.Services.AddScoped<ECNImageProcessingService>();
builder.Services.AddScoped<ECNFactoryOcrService>();
builder.Services.AddScoped<PSMCEcnDataService>();
builder.Services.AddScoped<ECNExcelProcessingService>();
builder.Services.AddScoped<ECNDatabaseService>();
builder.Services.AddScoped<KDBOMService>();

// Add DbContextFactory for ApplicationDbContext


var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();

app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();


app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

//app.MapControllers();

app.Run();
