﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\**" />
    <Compile Remove="wwwroot\data\**" />
    <Compile Remove="wwwroot\enc\**" />
    <Content Remove="Controllers\**" />
    <Content Remove="wwwroot\data\**" />
    <Content Remove="wwwroot\enc\**" />
    <EmbeddedResource Remove="Controllers\**" />
    <EmbeddedResource Remove="wwwroot\data\**" />
    <EmbeddedResource Remove="wwwroot\enc\**" />
    <None Remove="Controllers\**" />
    <None Remove="wwwroot\data\**" />
    <None Remove="wwwroot\enc\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="dapper" Version="2.1.66" />
    <PackageReference Include="epplus" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.6" />
    <PackageReference Include="microsoft.entityframeworkcore" Version="9.0.6" />
    <PackageReference Include="microsoft.entityframeworkcore.design" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="9.0.6" />
    <PackageReference Include="microsoft.entityframeworkcore.tools" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.5" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.5" />
    <PackageReference Include="syncfusion.blazor" Version="30.1.38" />
    <PackageReference Include="syncfusion.blazor.themes" Version="30.1.38" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="Tesseract" Version="5.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTO\" />
    <Folder Include="Models\" />
    <Folder Include="Data\" />
    <Folder Include="Services\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>
</Project>
