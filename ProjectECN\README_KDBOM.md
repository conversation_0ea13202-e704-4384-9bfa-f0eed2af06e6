# KDBOM Import System

This document describes the KDBOM (KD Bill of Materials) import system that has been implemented in the ProjectECN application.

## Overview

The KDBOM import system allows users to:
- Import multiple Excel files containing KDBOM data
- Process multiple sheets within each Excel file
- Store model information from the first sheet
- Store parts data from subsequent sheets
- View and search imported data using a professional grid interface
- Track file upload history and processing status

## Database Structure

### Tables Created

The system creates three main tables in the `kdbom` schema:

1. **kdbom.FileUploads** - Tracks uploaded files and processing status
2. **kdbom.ModelInfo** - Stores model information from the first sheet
3. **kdbom.PartsData** - Stores parts data from subsequent sheets

### SQL Script

Run the SQL script located at `ProjectECN/SQL/CreateKDBOMTables.sql` to create the required database structure:

```sql
-- Execute this script in your SQL Server database
-- File: ProjectECN/SQL/CreateKDBOMTables.sql
```

## Features Implemented

### 1. KDBOM Import Page (`/kdbom/import`)

**Features:**
- Multiple file selection (up to 20 Excel files)
- File validation and size display
- Progress tracking during import
- Automatic data truncation before import
- Detailed import results with statistics
- Error handling and reporting

**User Interface:**
- Clean, professional design following existing theme
- Compact layout with minimal padding/margins
- Native HTML file input controls
- Real-time progress indicators
- Success/error messaging with detailed statistics

### 2. KDBOM Data View Page (`/kdbom/data`)

**Features:**
- Syncfusion Grid (SfGrid) for data display
- Search functionality across multiple fields
- Sheet-based filtering
- Pagination with configurable page sizes
- Column sorting and filtering
- Responsive design
- File upload history display

**Grid Columns:**
- File Name
- Sheet Name
- Model Code
- Part Number
- Install Location
- Quantity
- BOM Name
- Dealer
- CC Code
- Uploaded By
- Upload Date

### 3. Data Processing

**Excel Processing:**
- Supports .xlsx and .xls files
- Processes first sheet as model information
- Processes subsequent sheets as parts data
- Automatic column mapping based on headers
- Handles missing or empty data gracefully
- Comprehensive error logging

**Column Mapping:**
The system maps Excel columns to database fields based on the sample data structure:
- KD Model Code
- KDV, Dealer, LVL
- Send TYPE, KD REFNO, Part NO
- Install Location Comment, QTY
- And many more fields as defined in the sample CSV

## Navigation

Two new navigation links have been added to the main menu:
- **KDBOM Import** - Navigate to import page
- **KDBOM Data** - Navigate to data view page

## Services

### KDBOMService

The main service class that handles:
- Excel file processing
- Database operations
- Data retrieval with filtering and pagination
- File upload tracking

**Key Methods:**
- `ProcessKDBOMFilesAsync()` - Process multiple Excel files
- `GetPartsDataAsync()` - Retrieve parts data with pagination
- `GetUploadedFilesAsync()` - Get file upload history
- `GetDistinctSheetNamesAsync()` - Get sheet names for filtering

## Models and DTOs

### Entity Models
- `FileUpload` - File upload tracking
- `ModelInfo` - Model information from first sheet
- `PartsData` - Parts data from subsequent sheets

### DTOs
- `KDBOMUploadResult` - Import operation results
- `KDBOMUploadProgress` - Progress tracking
- `KDBOMFileInfo` - File information display
- `KDBOMPartsDataDto` - Parts data for grid display

## Usage Instructions

### 1. Database Setup
1. Run the SQL script `ProjectECN/SQL/CreateKDBOMTables.sql` in your SQL Server database
2. Ensure the connection string in `appsettings.json` is correct

### 2. Import KDBOM Files
1. Navigate to `/kdbom/import`
2. Click "Browse Files" to select multiple Excel files
3. Review selected files
4. Click "Import Files" to start processing
5. Monitor progress and review results

### 3. View Imported Data
1. Navigate to `/kdbom/data`
2. Use search box to find specific parts or models
3. Use sheet filter to view data from specific sheets
4. Use grid features for sorting and filtering
5. View file upload history at the bottom

## Technical Notes

### File Processing
- Maximum file size: 200MB per file
- Maximum files per import: 20 files
- Supported formats: .xlsx, .xls
- Processing uses memory streams to handle large files efficiently

### Data Truncation
- The system truncates all existing KDBOM data before each import
- This ensures clean data without duplicates
- Users must select all files they want to import at once

### Error Handling
- Comprehensive error logging
- User-friendly error messages
- Partial success handling (some files may succeed while others fail)
- Detailed error reporting in import results

### Performance
- Database indexes on key fields for fast searching
- Pagination for large datasets
- Efficient column mapping
- Progress reporting for long operations

## Customization

### Adding New Columns
To add new columns to the parts data:
1. Add the column to the SQL table
2. Add the property to the `PartsData` entity
3. Update the column mapping in `KDBOMService`
4. Add the column to the grid in `ViewKDBOMData.razor`

### Modifying Import Logic
The import logic can be customized in the `KDBOMService` class:
- `ProcessModelInfoSheet()` - Customize model info extraction
- `ProcessPartsDataSheet()` - Customize parts data extraction
- `GetColumnMappings()` - Modify column mapping logic

## Security Considerations

- File size limits prevent large file attacks
- File type validation ensures only Excel files are processed
- User authentication should be implemented for production use
- Consider adding file scanning for malware in production

## Future Enhancements

Potential improvements:
- Export functionality (Excel, CSV)
- Data comparison between imports
- Advanced filtering options
- Bulk data editing
- Import scheduling
- Data validation rules
- Audit trail for data changes
