using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;

namespace ProjectECN.Services;

public class ECNImageProcessingService(ILogger<ECNImageProcessingService> logger)
{
    public async Task<ImageProcessingResult> ProcessECNImagesAsync(string extractedFolderPath,
        IProgress<ImageProcessingProgress>? progress = null)
    {
        try
        {
            logger.LogInformation($"Starting ECN image processing for folder: {extractedFolderPath}");

            var result = new ImageProcessingResult { Success = true };
            var processedCount = 0;
            var totalFiles = 0;

            // Find all TIFF files in the extracted folder structure
            var tiffFiles = FindAllTiffFiles(extractedFolderPath);
            totalFiles = tiffFiles.Count;

            logger.LogInformation($"Found {totalFiles} TIFF files to process");
            progress?.Report(new ImageProcessingProgress
                { Stage = "Scanning", ProcessedFiles = 0, TotalFiles = totalFiles });

            foreach (var tiffFile in tiffFiles)
                try
                {
                    logger.LogInformation($"Processing TIFF file: {tiffFile}");
                    await ProcessSingleTiffFile(tiffFile);
                    processedCount++;

                    progress?.Report(new ImageProcessingProgress
                    {
                        Stage = "Processing",
                        ProcessedFiles = processedCount,
                        TotalFiles = totalFiles,
                        CurrentFile = Path.GetFileName(tiffFile)
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Error processing TIFF file: {tiffFile}");
                    result.ErrorMessages.Add($"Error processing {Path.GetFileName(tiffFile)}: {ex.Message}");
                }

            result.ProcessedFiles = processedCount;
            result.TotalFiles = totalFiles;
            result.Message = $"Successfully processed {processedCount} of {totalFiles} TIFF files";

            logger.LogInformation($"ECN image processing completed. Processed: {processedCount}/{totalFiles}");
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during ECN image processing");
            return new ImageProcessingResult
            {
                Success = false,
                ErrorMessage = $"Image processing failed: {ex.Message}"
            };
        }
    }

    private List<string> FindAllTiffFiles(string rootPath)
    {
        var tiffFiles = new List<string>();

        if (!Directory.Exists(rootPath))
        {
            logger.LogWarning($"Root path does not exist: {rootPath}");
            return tiffFiles;
        }

        // Look for date folders (YYYYMMDD format)
        var dateFolders = Directory.GetDirectories(rootPath)
            .Where(dir => IsValidDateFolder(Path.GetFileName(dir)))
            .ToList();

        foreach (var dateFolder in dateFolders)
        {
            logger.LogInformation($"Scanning date folder: {dateFolder}");

            // Look for 2W and 4W folders
            var wheelFolders = Directory.GetDirectories(dateFolder)
                .Where(dir => Path.GetFileName(dir).Equals("2W", StringComparison.OrdinalIgnoreCase) ||
                              Path.GetFileName(dir).Equals("4W", StringComparison.OrdinalIgnoreCase))
                .ToList();

            foreach (var wheelFolder in wheelFolders)
            {
                // Recursively find all TIFF files in this wheel folder
                var tiffFilesInFolder = Directory.GetFiles(wheelFolder, "*.tif", SearchOption.AllDirectories)
                    .Concat(Directory.GetFiles(wheelFolder, "*.tiff", SearchOption.AllDirectories))
                    .ToList();

                tiffFiles.AddRange(tiffFilesInFolder);
                logger.LogInformation($"Found {tiffFilesInFolder.Count} TIFF files in {wheelFolder}");
            }
        }

        return tiffFiles;
    }

    private bool IsValidDateFolder(string folderName)
    {
        return folderName.Length == 8 &&
               folderName.All(char.IsDigit) &&
               DateTime.TryParseExact(folderName, "yyyyMMdd", null, DateTimeStyles.None, out _);
    }

    private async Task ProcessSingleTiffFile(string tiffFilePath)
    {
        await Task.Run(() =>
        {
            using var image = Image.FromFile(tiffFilePath);
            // Get the first frame (page) of the TIFF
            image.SelectActiveFrame(FrameDimension.Page, 0);

            var fileName = Path.GetFileNameWithoutExtension(tiffFilePath);
            var directory = Path.GetDirectoryName(tiffFilePath);

            // Extract ECN Description Box (415px, 265px, 1295px wide, 610px height)
            ExtractImagePortion(image, directory, fileName, "_description.png", 416, 265, 1294, 612);

            // Extract ECN Factory Box (1456px, 877px, 254px wide, 360px height)
            ExtractImagePortion(image, directory, fileName, "_factory.png", 1457, 876, 253, 362);
        });
    }

    private void ExtractImagePortion(Image sourceImage, string outputDirectory, string baseFileName, string suffix,
        int x, int y, int width, int height)
    {
        try
        {
            // Ensure the extraction area is within the image bounds
            var actualX = Math.Max(0, Math.Min(x, sourceImage.Width - 1));
            var actualY = Math.Max(0, Math.Min(y, sourceImage.Height - 1));
            var actualWidth = Math.Min(width, sourceImage.Width - actualX);
            var actualHeight = Math.Min(height, sourceImage.Height - actualY);

            if (actualWidth <= 0 || actualHeight <= 0)
            {
                logger.LogWarning($"Invalid extraction area for {baseFileName}{suffix}");
                return;
            }

            var extractionRect = new Rectangle(actualX, actualY, actualWidth, actualHeight);
            var outputPath = Path.Combine(outputDirectory, $"{baseFileName}{suffix}");

            using var bitmap = new Bitmap(sourceImage);
            using var extractedPortion = bitmap.Clone(extractionRect, bitmap.PixelFormat);
            extractedPortion.Save(outputPath, ImageFormat.Png);
            logger.LogInformation($"Extracted image portion saved: {outputPath}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error extracting image portion {baseFileName}{suffix}");
            throw;
        }
    }
}

public class ImageProcessingResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public List<string> ErrorMessages { get; set; } = new();
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
}

public class ImageProcessingProgress
{
    public string Stage { get; set; } = string.Empty;
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
    public string CurrentFile { get; set; } = string.Empty;
    public int Percentage => TotalFiles > 0 ? ProcessedFiles * 100 / TotalFiles : 0;
}